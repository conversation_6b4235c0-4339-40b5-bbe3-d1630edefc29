# C/C++ build system timings
generate_cxx_metadata
  execute-generate-process
    exec-configure 1559ms
    [gap of 18ms]
  execute-generate-process completed in 1581ms
  [gap of 24ms]
generate_cxx_metadata completed in 1609ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 37ms
  [gap of 18ms]
generate_cxx_metadata completed in 80ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 24ms]
  create-invalidation-state 59ms
  [gap of 29ms]
generate_cxx_metadata completed in 112ms

