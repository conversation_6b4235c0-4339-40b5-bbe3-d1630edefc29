/**
 * Database Service
 *
 * Ce service gère le stockage et la récupération des données de l'application.
 * Pour simplifier, nous utilisons une base de données en mémoire (objets JavaScript),
 * mais cela pourrait être remplacé par une vraie base de données comme MongoDB, MySQL, etc.
 */

const mongoose = require('mongoose');
const Driver = require('../models/driver');
const logger = require('../utils/logger');

// Base de données en mémoire
const db = {
  rides: [],
  drivers: [],
  riders: []
};

class DatabaseService {
  constructor() {
    console.log('Database service initialized');

    // MongoDB connection
    this.connectToMongoDB();
  }

  // MongoDB connection
  async connectToMongoDB() {
    try {
      // Use environment variables for connection string in production
      const connectionString = process.env.MONGODB_URI || 'mongodb://localhost:27017/ride_sharing';

      // Set shorter timeout for faster fallback
      await mongoose.connect(connectionString, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        serverSelectionTimeoutMS: 2000, // 2 seconds timeout
        connectTimeoutMS: 2000, // 2 seconds timeout
        socketTimeoutMS: 2000, // 2 seconds timeout
        bufferMaxEntries: 0, // Disable mongoose buffering
        bufferCommands: false, // Disable mongoose buffering
      });

      logger.info('Connected to MongoDB');
    } catch (error) {
      logger.error(`MongoDB connection error: ${error.message}`);
      // Fall back to in-memory storage if MongoDB connection fails
      logger.info('Using in-memory storage as fallback');

      // Disconnect mongoose to prevent further connection attempts
      await mongoose.disconnect();
    }
  }

  /**
   * Ajoute une nouvelle demande de trajet
   * @param {Object} rideData - Données du trajet
   * @returns {Object} - La demande de trajet créée avec un ID
   */
  createRide(rideData) {
    const ride = {
      id: `ride_${Date.now()}`,
      ...rideData,
      createdAt: new Date(),
      status: 'pending'
    };

    db.rides.push(ride);
    console.log(`New ride created with ID: ${ride.id}`);

    return ride;
  }

  /**
   * Récupère toutes les demandes de trajet
   * @param {Object} filters - Filtres optionnels
   * @returns {Array} - Liste des demandes de trajet
   */
  getRides(filters = {}) {
    let rides = [...db.rides];

    // Appliquer les filtres
    if (filters.status) {
      rides = rides.filter(ride => ride.status === filters.status);
    }

    if (filters.driverId) {
      rides = rides.filter(ride => ride.driverId === filters.driverId);
    }

    if (filters.riderId) {
      rides = rides.filter(ride => ride.riderId === filters.riderId);
    }

    return rides;
  }

  /**
   * Récupère une demande de trajet par son ID
   * @param {string} rideId - ID de la demande de trajet
   * @returns {Object|null} - La demande de trajet ou null si non trouvée
   */
  getRideById(rideId) {
    return db.rides.find(ride => ride.id === rideId) || null;
  }

  /**
   * Met à jour une demande de trajet
   * @param {string} rideId - ID de la demande de trajet
   * @param {Object} updates - Mises à jour à appliquer
   * @returns {Object|null} - La demande de trajet mise à jour ou null si non trouvée
   */
  updateRide(rideId, updates) {
    const index = db.rides.findIndex(ride => ride.id === rideId);

    if (index === -1) {
      return null;
    }

    // Mettre à jour la demande de trajet
    db.rides[index] = {
      ...db.rides[index],
      ...updates,
      updatedAt: new Date()
    };

    return db.rides[index];
  }

  /**
   * Ajoute un nouveau conducteur
   * @param {Object} driverData - Données du conducteur
   * @returns {Object} - Le conducteur créé avec un ID
   */
  async createDriver(driverData) {
    try {
      // Try to save to MongoDB first
      if (mongoose.connection.readyState === 1) {
        const driverModel = new Driver(driverData);
        const savedDriver = await driverModel.save();
        console.log(`Driver saved to MongoDB: ${savedDriver._id}`);
        return savedDriver;
      } else {
        // Fall back to in-memory storage
        const driver = {
          id: `driver_${Date.now()}`,
          ...driverData,
          createdAt: new Date(),
          isAvailable: true
        };

        db.drivers.push(driver);
        console.log(`New driver created with ID: ${driver.id}`);

        return driver;
      }
    } catch (error) {
      console.error(`Error saving driver: ${error.message}`);
      // Fall back to in-memory storage
      const driver = {
        id: `driver_${Date.now()}`,
        ...driverData,
        createdAt: new Date(),
        isAvailable: true
      };

      db.drivers.push(driver);
      console.log(`New driver created with ID: ${driver.id} (after MongoDB error)`);

      return driver;
    }
  }

  /**
   * Récupère tous les conducteurs
   * @param {Object} filters - Filtres optionnels
   * @returns {Array} - Liste des conducteurs
   */
  async getDrivers(filters = {}) {
    try {
      // Try to get from MongoDB first
      if (mongoose.connection.readyState === 1) {
        const query = {};

        if (filters.isAvailable !== undefined) {
          query.status = filters.isAvailable ? 'available' : { $ne: 'available' };
        }

        const drivers = await Driver.find(query);
        if (drivers && drivers.length > 0) {
          console.log(`Retrieved ${drivers.length} drivers from MongoDB`);
          return drivers;
        }
      }

      // Fall back to in-memory storage
      let drivers = [...db.drivers];

      // Appliquer les filtres
      if (filters.isAvailable !== undefined) {
        drivers = drivers.filter(driver => driver.isAvailable === filters.isAvailable);
      }

      return drivers;
    } catch (error) {
      console.error(`Error getting drivers: ${error.message}`);
      // Fall back to in-memory storage
      let drivers = [...db.drivers];

      // Appliquer les filtres
      if (filters.isAvailable !== undefined) {
        drivers = drivers.filter(driver => driver.isAvailable === filters.isAvailable);
      }

      return drivers;
    }
  }

  /**
   * Récupère un conducteur par son ID
   * @param {string} driverId - ID du conducteur
   * @returns {Object|null} - Le conducteur ou null si non trouvé
   */
  async getDriverById(driverId) {
    try {
      // Try to get from MongoDB first
      if (mongoose.connection.readyState === 1) {
        const driver = await Driver.findById(driverId);
        if (driver) {
          return driver;
        }
      }

      // Fall back to in-memory storage
      return db.drivers.find(driver => driver.id === driverId) || null;
    } catch (error) {
      console.error(`Error getting driver by ID: ${error.message}`);
      // Fall back to in-memory storage
      return db.drivers.find(driver => driver.id === driverId) || null;
    }
  }

  /**
   * Met à jour un conducteur
   * @param {string} driverId - ID du conducteur
   * @param {Object} updates - Mises à jour à appliquer
   * @returns {Object|null} - Le conducteur mis à jour ou null si non trouvé
   */
  async updateDriver(driverId, updates) {
    try {
      // Try to update in MongoDB first
      if (mongoose.connection.readyState === 1) {
        const updatedDriver = await Driver.findByIdAndUpdate(
          driverId,
          { ...updates, updatedAt: new Date() },
          { new: true }
        );

        if (updatedDriver) {
          console.log(`Driver updated in MongoDB: ${driverId}`);
          return updatedDriver;
        }
      }

      // Fall back to in-memory storage
      const index = db.drivers.findIndex(driver => driver.id === driverId);

      if (index === -1) {
        return null;
      }

      // Mettre à jour le conducteur
      db.drivers[index] = {
        ...db.drivers[index],
        ...updates,
        updatedAt: new Date()
      };

      return db.drivers[index];
    } catch (error) {
      console.error(`Error updating driver: ${error.message}`);

      // Fall back to in-memory storage
      const index = db.drivers.findIndex(driver => driver.id === driverId);

      if (index === -1) {
        return null;
      }

      // Mettre à jour le conducteur
      db.drivers[index] = {
        ...db.drivers[index],
        ...updates,
        updatedAt: new Date()
      };

      return db.drivers[index];
    }
  }

  /**
   * Récupère les conducteurs proches d'une position donnée
   * @param {Object} center - Position centrale {lat, lng}
   * @param {number} radiusKm - Rayon de recherche en kilomètres
   * @param {number} limit - Nombre maximum de résultats
   * @returns {Array} - Liste des conducteurs triés par distance
   */
  async getDriversByLocation(center, radiusKm, limit = 10) {
    try {
      // Try to get from MongoDB first
      if (mongoose.connection.readyState === 1) {
        // Convert radius from kilometers to meters
        const radiusInMeters = radiusKm * 1000;

        const drivers = await Driver.find({
          location: {
            $near: {
              $geometry: {
                type: 'Point',
                coordinates: [center.lng, center.lat]
              },
              $maxDistance: radiusInMeters
            }
          },
          status: 'available'
        }).limit(limit);

        if (drivers && drivers.length > 0) {
          console.log(`Found ${drivers.length} nearby drivers in MongoDB`);
          return drivers;
        }
      }

      // Fall back to in-memory storage with simple distance calculation
      return db.drivers
        .filter(driver => driver.isAvailable === true)
        .map(driver => {
          const distance = this.calculateDistance(
            center.lat, center.lng,
            driver.location.lat, driver.location.lng
          );
          return { ...driver, distance };
        })
        .filter(driver => driver.distance <= radiusKm)
        .sort((a, b) => a.distance - b.distance)
        .slice(0, limit);
    } catch (error) {
      console.error(`Error getting drivers by location: ${error.message}`);
      // Fall back to in-memory storage
      return [];
    }
  }

  /**
   * Calculate distance between two points using Haversine formula
   * @param {number} lat1 - Latitude of first point
   * @param {number} lon1 - Longitude of first point
   * @param {number} lat2 - Latitude of second point
   * @param {number} lon2 - Longitude of second point
   * @returns {number} - Distance in kilometers
   */
  calculateDistance(lat1, lon1, lat2, lon2) {
    const R = 6371; // Radius of the earth in km
    const dLat = this.deg2rad(lat2 - lat1);
    const dLon = this.deg2rad(lon2 - lon1);
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.deg2rad(lat1)) * Math.cos(this.deg2rad(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in km
    return distance;
  }

  /**
   * Convert degrees to radians
   * @param {number} deg - Degrees
   * @returns {number} - Radians
   */
  deg2rad(deg) {
    return deg * (Math.PI / 180);
  }

  /**
   * Ajoute un nouveau passager
   * @param {Object} riderData - Données du passager
   * @returns {Object} - Le passager créé avec un ID
   */
  createRider(riderData) {
    const rider = {
      id: `rider_${Date.now()}`,
      ...riderData,
      createdAt: new Date()
    };

    db.riders.push(rider);
    console.log(`New rider created with ID: ${rider.id}`);

    return rider;
  }

  /**
   * Récupère un passager par son ID
   * @param {string} riderId - ID du passager
   * @returns {Object|null} - Le passager ou null si non trouvé
   */
  getRiderById(riderId) {
    return db.riders.find(rider => rider.id === riderId) || null;
  }
}

module.exports = new DatabaseService();
