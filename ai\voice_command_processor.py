#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Voice Command Processor for Smart Ride-Sharing Application

This service processes voice commands for the smart ride-sharing application.
It provides a REST API that the Flutter app can call to process voice commands.

Features:
- Speech-to-text conversion
- Natural language understanding for ride-sharing commands
- Command intent classification
- Location extraction from commands
- Response generation

Author: Smart Ride-Sharing Team
"""

import os
import json
import logging
import tempfile
from typing import Dict, List, Optional, Tuple, Union, Any
import uuid
import time
from datetime import datetime

import speech_recognition as sr
import numpy as np
from flask import Flask, request, jsonify
from flask_cors import CORS
import nltk
from nltk.tokenize import word_tokenize
from nltk.corpus import stopwords

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Download NLTK resources (only needed once)
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    nltk.download('stopwords')

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Initialize speech recognizer
recognizer = sr.Recognizer()

# Command keywords and their synonyms (Arabic and English)
COMMAND_KEYWORDS = {
    'request_ride': [
        'طلب رحلة', 'اطلب رحلة', 'أريد رحلة', 'احجز رحلة', 'خذني إلى',
        'book ride', 'request ride', 'order ride', 'get ride', 'take me to'
    ],
    'cancel_ride': [
        'إلغاء الرحلة', 'ألغي الرحلة', 'إلغاء', 'الغاء',
        'cancel ride', 'stop ride', 'end ride'
    ],
    'driver_location': [
        'أين السائق', 'وين السائق', 'موقع السائق', 'متى يصل السائق',
        'where is driver', 'driver location', 'when will driver arrive'
    ],
    'change_destination': [
        'تغيير الوجهة', 'غير الوجهة', 'تعديل الوجهة',
        'change destination', 'update destination', 'new destination'
    ],
    'share_ride': [
        'مشاركة الرحلة', 'شارك الرحلة', 'مشاركة',
        'share ride', 'share trip', 'share journey'
    ],
    'rate_ride': [
        'تقييم الرحلة', 'قيم الرحلة', 'تقييم',
        'rate ride', 'rate trip', 'review ride'
    ],
    'help': [
        'مساعدة', 'ساعدني', 'ما هي الأوامر المتاحة', 'كابتن', 'مرحبا كابتن',
        'help', 'assist me', 'what commands', 'captain', 'hello captain'
    ],
    'home': [
        'الصفحة الرئيسية', 'الرئيسية',
        'home', 'main screen', 'main page'
    ],
    'back': [
        'العودة', 'رجوع',
        'back', 'go back', 'return'
    ]
}

# Common locations (for demonstration purposes)
COMMON_LOCATIONS = {
    'home': {'lat': 30.0444, 'lng': 31.2357, 'address': 'Home Address'},
    'work': {'lat': 30.0626, 'lng': 31.2497, 'address': 'Work Address'},
    'airport': {'lat': 30.1219, 'lng': 31.4056, 'address': 'Cairo International Airport'},
    'mall': {'lat': 30.0484, 'lng': 31.2142, 'address': 'City Stars Mall'},
    'downtown': {'lat': 30.0454, 'lng': 31.2335, 'address': 'Downtown Cairo'},
    'university': {'lat': 30.0259, 'lng': 31.2122, 'address': 'Cairo University'},
    'hospital': {'lat': 30.0580, 'lng': 31.2269, 'address': 'Cairo Hospital'},
    'park': {'lat': 30.0286, 'lng': 31.2233, 'address': 'Al-Azhar Park'},
    'restaurant': {'lat': 30.0445, 'lng': 31.2240, 'address': 'Popular Restaurant'},
    'hotel': {'lat': 30.0571, 'lng': 31.2312, 'address': 'Luxury Hotel'}
}

# Active conversations
conversations = {}

class Conversation:
    """Class to manage conversation state"""
    def __init__(self, user_id: str):
        self.id = str(uuid.uuid4())
        self.user_id = user_id
        self.messages = []
        self.context = {
            "current_ride": None,
            "last_command": None,
            "pending_confirmation": None,
            "last_location": None
        }
        self.created_at = datetime.now()
        self.last_updated = datetime.now()

    def add_message(self, role: str, content: str):
        """Add a message to the conversation"""
        self.messages.append({
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat()
        })
        self.last_updated = datetime.now()

    def update_context(self, key: str, value: Any):
        """Update the conversation context"""
        self.context[key] = value
        self.last_updated = datetime.now()

    def to_dict(self):
        """Convert conversation to dictionary"""
        return {
            "id": self.id,
            "user_id": self.user_id,
            "messages": self.messages,
            "context": self.context,
            "created_at": self.created_at.isoformat(),
            "last_updated": self.last_updated.isoformat()
        }

def recognize_speech_from_audio(audio_data: Union[bytes, sr.AudioData]) -> Dict:
    """
    Convert speech to text using Google's speech recognition API.

    Args:
        audio_data: Audio data as bytes or AudioData object

    Returns:
        Dict containing recognition result or error
    """
    # If audio_data is bytes, convert to AudioData
    if isinstance(audio_data, bytes):
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_audio:
            temp_audio.write(audio_data)
            temp_filename = temp_audio.name

        try:
            with sr.AudioFile(temp_filename) as source:
                audio_data = recognizer.record(source)
            os.unlink(temp_filename)  # Delete temp file
        except Exception as e:
            if os.path.exists(temp_filename):
                os.unlink(temp_filename)
            return {'success': False, 'error': f'Error processing audio file: {str(e)}'}

    try:
        # Try Arabic recognition first
        try:
            text = recognizer.recognize_google(audio_data, language='ar-SA')
            return {'success': True, 'text': text, 'language': 'ar-SA'}
        except sr.UnknownValueError:
            # If Arabic fails, try English
            text = recognizer.recognize_google(audio_data, language='en-US')
            return {'success': True, 'text': text, 'language': 'en-US'}
    except sr.UnknownValueError:
        return {'success': False, 'error': 'Could not understand audio'}
    except sr.RequestError as e:
        return {'success': False, 'error': f'Speech recognition service error: {e}'}
    except Exception as e:
        return {'success': False, 'error': f'Error processing audio: {str(e)}'}

def extract_location(text: str) -> Optional[Dict]:
    """
    Extract location information from the command text.

    Args:
        text: The command text

    Returns:
        Location information if found, None otherwise
    """
    text_lower = text.lower()

    # Check for common locations
    for location_name, location_data in COMMON_LOCATIONS.items():
        if location_name in text_lower or location_name.title() in text:
            return {
                'name': location_name,
                'lat': location_data['lat'],
                'lng': location_data['lng'],
                'address': location_data['address']
            }

    # Check for "to [location]" pattern in English
    if "to " in text_lower:
        parts = text_lower.split("to ")
        if len(parts) > 1:
            for location_name in COMMON_LOCATIONS:
                if location_name in parts[1]:
                    location_data = COMMON_LOCATIONS[location_name]
                    return {
                        'name': location_name,
                        'lat': location_data['lat'],
                        'lng': location_data['lng'],
                        'address': location_data['address']
                    }

    # Check for "إلى [location]" pattern in Arabic
    if "إلى " in text or "الى " in text:
        parts = text.replace("الى ", "إلى ").split("إلى ")
        if len(parts) > 1:
            for location_name in COMMON_LOCATIONS:
                if location_name in parts[1].lower():
                    location_data = COMMON_LOCATIONS[location_name]
                    return {
                        'name': location_name,
                        'lat': location_data['lat'],
                        'lng': location_data['lng'],
                        'address': location_data['address']
                    }

    return None

def identify_command(text: str, conversation_context: Optional[Dict] = None) -> Tuple[str, float, Optional[Dict]]:
    """
    Identify the command type from the text.

    Args:
        text: The command text
        conversation_context: Optional context from previous conversation

    Returns:
        Tuple of (command_type, confidence, location_data)
    """
    text_lower = text.lower()

    # Check for exact matches first
    for cmd_type, keywords in COMMAND_KEYWORDS.items():
        for keyword in keywords:
            if keyword.lower() in text_lower:
                location_data = extract_location(text) if cmd_type == 'request_ride' or cmd_type == 'change_destination' else None
                return cmd_type, 0.9, location_data

    # If no exact match, use more sophisticated NLP
    tokens = word_tokenize(text_lower)

    # Count matches for each command type
    command_scores = {cmd_type: 0 for cmd_type in COMMAND_KEYWORDS}

    for token in tokens:
        for cmd_type, keywords in COMMAND_KEYWORDS.items():
            for keyword in keywords:
                if token in keyword.lower().split():
                    command_scores[cmd_type] += 1

    # Find the command with the highest score
    max_score = max(command_scores.values())
    if max_score == 0:
        return 'unknown', 0.0, None

    # Get all commands with the max score
    top_commands = [cmd for cmd, score in command_scores.items() if score == max_score]

    # If there's a tie, use context to disambiguate
    if len(top_commands) > 1:
        # Prioritize based on context
        if conversation_context and conversation_context.get('last_command') == 'request_ride':
            if 'change_destination' in top_commands:
                command_type = 'change_destination'
            elif 'cancel_ride' in top_commands:
                command_type = 'cancel_ride'
            else:
                command_type = top_commands[0]
        else:
            # Prioritize set_destination if a location is mentioned
            location_data = extract_location(text)
            if location_data and 'request_ride' in top_commands:
                command_type = 'request_ride'
            else:
                command_type = top_commands[0]
    else:
        command_type = top_commands[0]

    # Calculate confidence (normalized score)
    confidence = min(1.0, max_score / (len(tokens) * 0.5))

    # Extract location if relevant
    location_data = extract_location(text) if command_type == 'request_ride' or command_type == 'change_destination' else None

    return command_type, confidence, location_data

def process_command(text: str, user_id: str = "anonymous", conversation_id: Optional[str] = None) -> Dict:
    """
    Process the command text and return an appropriate response.

    Args:
        text: The command text
        user_id: User identifier
        conversation_id: Optional conversation ID for context

    Returns:
        Dict containing the response
    """
    # Get or create conversation
    conversation = None
    if conversation_id and conversation_id in conversations:
        conversation = conversations[conversation_id]
    else:
        conversation = Conversation(user_id)
        conversations[conversation.id] = conversation

    # Add user message to conversation
    conversation.add_message("user", text)

    # Identify command
    command_type, confidence, location_data = identify_command(text, conversation.context)

    # Update conversation context
    conversation.update_context("last_command", command_type)
    if location_data:
        conversation.update_context("last_location", location_data)

    response = {
        'success': True,
        'conversation_id': conversation.id,
        'command': {
            'type': command_type,
            'text': text,
            'confidence': confidence
        },
        'response': {}
    }

    # Generate appropriate response based on command type
    if command_type == 'request_ride':
        if location_data:
            response_text = f'جاري طلب رحلة إلى {location_data["name"]}. هل تريد تأكيد الطلب؟'
            response['response'] = {
                'message': response_text,
                'action': 'request_ride',
                'location': location_data,
                'requires_confirmation': True
            }
        else:
            response_text = 'إلى أين تريد الذهاب؟'
            response['response'] = {
                'message': response_text,
                'action': 'request_destination',
                'requires_location': True
            }
        conversation.update_context("pending_confirmation", "request_ride")

    elif command_type == 'cancel_ride':
        response_text = 'هل أنت متأكد من رغبتك في إلغاء الرحلة؟'
        response['response'] = {
            'message': response_text,
            'action': 'confirm_cancellation',
            'requires_confirmation': True
        }
        conversation.update_context("pending_confirmation", "cancel_ride")

    elif command_type == 'driver_location':
        # In a real app, this would fetch actual driver location
        response_text = 'السائق في الطريق إليك، سيصل خلال 5 دقائق تقريباً.'
        response['response'] = {
            'message': response_text,
            'action': 'show_driver_location',
            'driver_eta': 5,
            'driver': {
                'name': 'أحمد',
                'car': 'تويوتا كورولا',
                'plate': 'ABC 123',
                'rating': 4.8
            }
        }

    elif command_type == 'change_destination':
        if location_data:
            response_text = f'جاري تغيير الوجهة إلى {location_data["name"]}. هل تريد تأكيد التغيير؟'
            response['response'] = {
                'message': response_text,
                'action': 'change_destination',
                'location': location_data,
                'requires_confirmation': True
            }
        else:
            response_text = 'إلى أين تريد تغيير وجهتك؟'
            response['response'] = {
                'message': response_text,
                'action': 'request_new_destination',
                'requires_location': True
            }
        conversation.update_context("pending_confirmation", "change_destination")

    elif command_type == 'share_ride':
        response_text = 'يمكنك مشاركة تفاصيل رحلتك مع أصدقائك. مع من تريد المشاركة؟'
        response['response'] = {
            'message': response_text,
            'action': 'show_sharing_options',
            'sharing_options': ['WhatsApp', 'SMS', 'Email', 'Twitter']
        }

    elif command_type == 'rate_ride':
        response_text = 'يرجى تقييم الرحلة من 1 إلى 5 نجوم.'
        response['response'] = {
            'message': response_text,
            'action': 'show_rating_dialog'
        }

    elif command_type == 'help':
        response_text = 'يمكنني مساعدتك في: طلب رحلة، إلغاء الرحلة، معرفة موقع السائق، تغيير الوجهة، مشاركة الرحلة، وتقييم الرحلة.'
        response['response'] = {
            'message': response_text,
            'action': 'show_help',
            'commands': list(COMMAND_KEYWORDS.keys())
        }

    elif command_type == 'home':
        response_text = 'جاري الانتقال إلى الصفحة الرئيسية.'
        response['response'] = {
            'message': response_text,
            'action': 'navigate_to_home'
        }

    elif command_type == 'back':
        response_text = 'جاري العودة للخلف.'
        response['response'] = {
            'message': response_text,
            'action': 'navigate_back'
        }

    else:
        response_text = 'عذراً، لم أفهم طلبك. يمكنك قول "مساعدة" للحصول على قائمة بالأوامر المتاحة.'
        response['response'] = {
            'message': response_text,
            'action': 'show_error',
            'error': 'unknown_command'
        }

    # Add assistant message to conversation
    conversation.add_message("assistant", response_text)

    return response

@app.route('/api/voice/process', methods=['POST'])
def process_voice():
    """API endpoint to process voice commands from audio file or text"""
    try:
        user_id = request.headers.get('X-User-ID', 'anonymous')
        conversation_id = request.headers.get('X-Conversation-ID')

        if 'audio' in request.files:
            # Process audio file
            audio_file = request.files['audio']
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_audio:
                audio_file.save(temp_audio.name)
                temp_filename = temp_audio.name

            try:
                with sr.AudioFile(temp_filename) as source:
                    audio_data = recognizer.record(source)
                os.unlink(temp_filename)  # Delete temp file

                speech_result = recognize_speech_from_audio(audio_data)
                if not speech_result['success']:
                    return jsonify(speech_result), 400

                text = speech_result['text']
                logger.info(f"Recognized text: {text}")
            except Exception as e:
                if os.path.exists(temp_filename):
                    os.unlink(temp_filename)
                return jsonify({
                    'success': False,
                    'error': f'Error processing audio file: {str(e)}'
                }), 400
        elif request.json and 'text' in request.json:
            # Process text directly
            text = request.json['text']
            logger.info(f"Processing text: {text}")
        else:
            return jsonify({
                'success': False,
                'error': 'No audio file or text provided'
            }), 400

        # Process the command
        result = process_command(text, user_id, conversation_id)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error processing request: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Server error: {str(e)}'
        }), 500

@app.route('/api/voice/listen', methods=['POST'])
def listen_voice():
    """API endpoint to listen for voice commands"""
    try:
        timeout = request.json.get('timeout', 5) if request.json else 5

        # Initialize recognizer
        r = sr.Recognizer()

        # Use microphone as source
        with sr.Microphone() as source:
            logger.info(f"Listening for {timeout} seconds...")
            r.adjust_for_ambient_noise(source)
            audio = r.listen(source, timeout=timeout)

        # Recognize speech
        speech_result = recognize_speech_from_audio(audio)
        return jsonify(speech_result)

    except Exception as e:
        logger.error(f"Error listening: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error listening: {str(e)}'
        }), 500

@app.route('/api/voice/speak', methods=['POST'])
def speak_text():
    """API endpoint to convert text to speech"""
    try:
        if not request.json or 'text' not in request.json:
            return jsonify({
                'success': False,
                'error': 'No text provided'
            }), 400

        text = request.json['text']
        language = request.json.get('language', 'ar-SA')

        # In a real implementation, this would use a TTS engine
        # For now, we just return success
        logger.info(f"Speaking text: {text} in {language}")

        return jsonify({
            'success': True,
            'text': text,
            'language': language
        })

    except Exception as e:
        logger.error(f"Error speaking: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error speaking: {str(e)}'
        }), 500

@app.route('/api/voice/command', methods=['POST'])
def direct_command():
    """API endpoint to process a command directly"""
    try:
        if not request.json or 'command' not in request.json:
            return jsonify({
                'success': False,
                'error': 'No command provided'
            }), 400

        command = request.json['command']
        user_id = request.headers.get('X-User-ID', 'anonymous')
        conversation_id = request.headers.get('X-Conversation-ID')

        # Process the command
        result = process_command(command, user_id, conversation_id)
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error processing command: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error processing command: {str(e)}'
        }), 500

@app.route('/api/voice/conversations', methods=['GET'])
def get_conversations():
    """API endpoint to get all conversations"""
    try:
        user_id = request.headers.get('X-User-ID')

        if user_id:
            # Filter conversations by user ID
            user_conversations = {
                conv_id: conv.to_dict()
                for conv_id, conv in conversations.items()
                if conv.user_id == user_id
            }
            return jsonify({
                'success': True,
                'conversations': user_conversations
            })
        else:
            # Return all conversations
            return jsonify({
                'success': True,
                'conversations': {
                    conv_id: conv.to_dict()
                    for conv_id, conv in conversations.items()
                }
            })

    except Exception as e:
        logger.error(f"Error getting conversations: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error getting conversations: {str(e)}'
        }), 500

@app.route('/api/voice/conversations/<conversation_id>', methods=['GET'])
def get_conversation(conversation_id):
    """API endpoint to get a specific conversation"""
    try:
        if conversation_id not in conversations:
            return jsonify({
                'success': False,
                'error': f'Conversation {conversation_id} not found'
            }), 404

        return jsonify({
            'success': True,
            'conversation': conversations[conversation_id].to_dict()
        })

    except Exception as e:
        logger.error(f"Error getting conversation: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'Error getting conversation: {str(e)}'
        }), 500

@app.route('/api/voice/status', methods=['GET'])
def service_status():
    """API endpoint to check if the service is running"""
    return jsonify({
        'success': True,
        'status': 'running',
        'version': '1.0.0',
        'active_conversations': len(conversations)
    })

if __name__ == '__main__':
    # Start the Flask app
    logger.info("Starting voice command processor service...")
    app.run(host='0.0.0.0', port=5000, debug=False)
