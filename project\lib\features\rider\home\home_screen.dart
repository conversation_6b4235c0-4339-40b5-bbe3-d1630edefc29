import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:project/core/config/api_config.dart';
import 'package:project/core/services/socket_service.dart';
import 'dart:async';

/// Enhanced Home Screen with Backend Integration and AI Features
/// This screen provides real ride booking functionality with AI assistance
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TextEditingController _searchController;
  late TextEditingController _destinationController;
  late AnimationController _pulseController;
  late AnimationController _slideController;
  late Animation<double> _pulseAnimation;
  late Animation<Offset> _slideAnimation;

  // State variables
  bool _isRequestingRide = false;
  bool _isListeningToVoice = false;
  String? _currentRideId;
  String? _rideStatus;
  Map<String, dynamic>? _currentLocation;
  Map<String, dynamic>? _destinationLocation;
  List<Map<String, dynamic>> _recentDestinations = [];
  String _aiSuggestion = '';
  bool _showAiSuggestion = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _destinationController = TextEditingController();

    // Initialize animations
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    _initializeApp();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _destinationController.dispose();
    _pulseController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _initializeApp() async {
    // Initialize socket connection
    SocketService.instance.connect();

    // Load user data and recent destinations
    await _loadUserData();
    await _loadRecentDestinations();

    // Set up socket listeners
    _setupSocketListeners();

    // Get AI suggestions
    await _getAiSuggestions();
  }

  Future<void> _loadUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id') ??
          'user_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString('user_id', userId);

      // Set default current location (San Francisco)
      _currentLocation = {
        'lat': 37.7749,
        'lng': -122.4194,
        'address': 'San Francisco, CA'
      };
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  Future<void> _loadRecentDestinations() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final recentJson = prefs.getString('recent_destinations');
      if (recentJson != null) {
        final List<dynamic> decoded = jsonDecode(recentJson);
        setState(() {
          _recentDestinations = decoded.cast<Map<String, dynamic>>();
        });
      }
    } catch (e) {
      debugPrint('Error loading recent destinations: $e');
    }
  }

  void _setupSocketListeners() {
    // Listen for ride status updates
    SocketService.instance.listenTo('ride:accepted', (data) {
      if (mounted) {
        setState(() {
          _rideStatus = 'accepted';
          _currentRideId = data['rideId'];
        });
        _showRideStatusDialog(
            'Ride Accepted!', 'A driver has accepted your ride request.');
      }
    });

    SocketService.instance.listenTo('ride:started', (data) {
      if (mounted) {
        setState(() {
          _rideStatus = 'in_progress';
        });
        _showRideStatusDialog('Ride Started!', 'Your driver is on the way.');
      }
    });

    SocketService.instance.listenTo('ride:completed', (data) {
      if (mounted) {
        setState(() {
          _rideStatus = 'completed';
          _currentRideId = null;
        });
        _showRideStatusDialog(
            'Ride Completed!', 'Thank you for using our service.');
      }
    });
  }

  Future<void> _getAiSuggestions() async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/voice/command'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'command': 'suggest popular destinations',
          'auto_speak': false,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        setState(() {
          _aiSuggestion = data['response'] ?? 'No suggestions available';
          _showAiSuggestion = true;
        });
        _slideController.forward();
      }
    } catch (e) {
      debugPrint('Error getting AI suggestions: $e');
    }
  }

  void _showRideStatusDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            const Icon(Icons.info, color: Colors.blue),
            const SizedBox(width: 8),
            Text(title),
          ],
        ),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _requestRide() async {
    if (_searchController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a destination')),
      );
      return;
    }

    setState(() {
      _isRequestingRide = true;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id') ??
          'user_${DateTime.now().millisecondsSinceEpoch}';

      // Create destination location from input
      _destinationLocation = {
        'lat': 37.7849, // Mock destination coordinates
        'lng': -122.4094,
        'address': _searchController.text,
      };

      // Send ride request via Socket.IO
      SocketService.instance.requestRide(
        riderId: userId,
        pickupLocation: _currentLocation!,
        destination: _destinationLocation!,
      );

      // Also send via HTTP API as backup
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/rides'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'riderId': userId,
          'pickupLocation': _currentLocation,
          'destination': _destinationLocation,
        }),
      );

      if (response.statusCode == 201) {
        final data = jsonDecode(response.body);
        setState(() {
          _currentRideId = data['rideId'];
          _rideStatus = 'pending';
        });

        // Save to recent destinations
        await _saveToRecentDestinations(_destinationLocation!);

        // Get AI prediction for ride sharing
        await _getAiRideSharingPrediction();

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content:
                Text('Ride requested successfully! Searching for drivers...'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        throw Exception('Failed to request ride');
      }
    } catch (e) {
      debugPrint('Error requesting ride: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error requesting ride: $e')),
      );
    } finally {
      setState(() {
        _isRequestingRide = false;
      });
    }
  }

  Future<void> _getAiRideSharingPrediction() async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/ride-sharing/predict'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'original_distance': 10.0,
          'distance_after_adding_rider': 12.0,
          'new_rider_distance': 3.5,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _showAiPredictionDialog(data);
      }
    } catch (e) {
      debugPrint('Error getting AI prediction: $e');
    }
  }

  Future<void> _saveToRecentDestinations(
      Map<String, dynamic> destination) async {
    try {
      // Add to recent destinations (max 5)
      _recentDestinations.insert(0, destination);
      if (_recentDestinations.length > 5) {
        _recentDestinations = _recentDestinations.take(5).toList();
      }

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
          'recent_destinations', jsonEncode(_recentDestinations));

      setState(() {});
    } catch (e) {
      debugPrint('Error saving recent destination: $e');
    }
  }

  void _showAiPredictionDialog(Map<String, dynamic> prediction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.psychology, color: Colors.purple),
            SizedBox(width: 8),
            Text('AI Ride Sharing Analysis'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
                'AI Recommendation: ${prediction['shouldAddRider'] ? 'Share this ride' : 'Keep ride private'}'),
            const SizedBox(height: 8),
            Text(
                'Confidence: ${(prediction['score'] * 100).toStringAsFixed(1)}%'),
            if (prediction['efficiency'] != null)
              Text('Efficiency: ${prediction['efficiency']}%'),
            const SizedBox(height: 16),
            const Text('Benefits of ride sharing:',
                style: TextStyle(fontWeight: FontWeight.bold)),
            const Text('• Reduce costs'),
            const Text('• Environmental friendly'),
            const Text('• Meet new people'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          if (prediction['shouldAddRider'])
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content:
                          Text('Looking for ride sharing opportunities...')),
                );
              },
              child: const Text('Find Sharing'),
            ),
        ],
      ),
    );
  }

  Future<void> _startVoiceCommand() async {
    setState(() {
      _isListeningToVoice = true;
    });
    _pulseController.repeat(reverse: true);

    try {
      // Simulate voice recognition (in real app, use speech_to_text package)
      await Future.delayed(const Duration(seconds: 3));

      final voiceCommand = 'Take me to the airport'; // Mock voice input

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/voice/command'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'command': voiceCommand,
          'auto_speak': true,
        }),
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        _searchController.text = 'Airport';

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Voice command: ${data['response']}')),
        );
      }
    } catch (e) {
      debugPrint('Error processing voice command: $e');
    } finally {
      setState(() {
        _isListeningToVoice = false;
      });
      _pulseController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Smart Ride Sharing'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: Stack(
              children: [
                const Icon(Icons.notifications),
                if (_currentRideId != null)
                  Positioned(
                    right: 0,
                    top: 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 12,
                        minHeight: 12,
                      ),
                    ),
                  ),
              ],
            ),
            onPressed: () {
              if (_currentRideId != null) {
                _showRideStatusDialog('Current Ride',
                    'Ride ID: $_currentRideId\nStatus: $_rideStatus');
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('No active rides')),
                );
              }
            },
          ),
        ],
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            const DrawerHeader(
              decoration: BoxDecoration(
                color: Colors.blue,
              ),
              child: Text(
                'Menu',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.home),
              title: const Text('Home'),
              onTap: () => Navigator.pop(context),
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('Ride History'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Ride History - Coming Soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('Profile'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Profile - Coming Soon')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Settings'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Settings - Coming Soon')),
                );
              },
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Status bar for active ride
          if (_currentRideId != null) _buildRideStatusBar(),

          // AI Suggestion banner
          if (_showAiSuggestion) _buildAiSuggestionBanner(),

          // Enhanced search section
          _buildSearchSection(),

          // Map placeholder
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.map,
                      size: 80,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Map View',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'Interactive map will be loaded here',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Bottom buttons
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isRequestingRide ? null : _requestRide,
                    icon: const Icon(Icons.local_taxi),
                    label: const Text('Request Ride'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: _isListeningToVoice ? null : _startVoiceCommand,
                  icon: const Icon(Icons.mic),
                  label: const Text('Voice'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ],
            ),
          ),

          // Quick actions
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildQuickAction(
                  icon: Icons.share,
                  label: 'Share Ride',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Share Ride - Coming Soon')),
                    );
                  },
                ),
                _buildQuickAction(
                  icon: Icons.schedule,
                  label: 'Schedule',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Schedule Ride - Coming Soon')),
                    );
                  },
                ),
                _buildQuickAction(
                  icon: Icons.favorite,
                  label: 'Favorites',
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                          content: Text('Favorite Places - Coming Soon')),
                    );
                  },
                ),
              ],
            ),
          ),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildRideStatusBar() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _rideStatus == 'pending'
            ? Colors.orange[100]
            : _rideStatus == 'accepted'
                ? Colors.blue[100]
                : _rideStatus == 'in_progress'
                    ? Colors.green[100]
                    : Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _rideStatus == 'pending'
              ? Colors.orange
              : _rideStatus == 'accepted'
                  ? Colors.blue
                  : _rideStatus == 'in_progress'
                      ? Colors.green
                      : Colors.grey,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _rideStatus == 'pending'
                ? Icons.hourglass_empty
                : _rideStatus == 'accepted'
                    ? Icons.check_circle
                    : _rideStatus == 'in_progress'
                        ? Icons.directions_car
                        : Icons.info,
            color: _rideStatus == 'pending'
                ? Colors.orange
                : _rideStatus == 'accepted'
                    ? Colors.blue
                    : _rideStatus == 'in_progress'
                        ? Colors.green
                        : Colors.grey,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Current Ride',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[800],
                  ),
                ),
                Text(
                  'Status: ${_rideStatus?.toUpperCase()}',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: () => _showRideStatusDialog('Ride Details',
                'Ride ID: $_currentRideId\nStatus: $_rideStatus'),
            child: const Text('Details'),
          ),
        ],
      ),
    );
  }

  Widget _buildAiSuggestionBanner() {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.purple[100]!, Colors.blue[100]!],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            const Icon(Icons.psychology, color: Colors.purple),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'AI Suggestion',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.purple,
                    ),
                  ),
                  Text(
                    _aiSuggestion,
                    style: TextStyle(color: Colors.grey[700]),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () {
                setState(() {
                  _showAiSuggestion = false;
                });
                _slideController.reverse();
              },
              icon: const Icon(Icons.close, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // Main search bar
          Card(
            elevation: 4,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                controller: _searchController,
                decoration: const InputDecoration(
                  hintText: 'Where would you like to go?',
                  border: InputBorder.none,
                  icon: Icon(Icons.search, color: Colors.blue),
                  suffixIcon: Icon(Icons.my_location, color: Colors.grey),
                ),
                onSubmitted: (value) {
                  if (value.isNotEmpty) {
                    _requestRide();
                  }
                },
              ),
            ),
          ),

          // Recent destinations
          if (_recentDestinations.isNotEmpty) ...[
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'Recent Destinations',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ),
            const SizedBox(height: 8),
            SizedBox(
              height: 40,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _recentDestinations.length,
                itemBuilder: (context, index) {
                  final destination = _recentDestinations[index];
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: ActionChip(
                      label: Text(destination['address']),
                      onPressed: () {
                        _searchController.text = destination['address'];
                      },
                      backgroundColor: Colors.blue[50],
                    ),
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: Colors.blue,
              size: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
