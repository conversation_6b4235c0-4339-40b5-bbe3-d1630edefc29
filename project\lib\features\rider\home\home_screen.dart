import 'dart:async';
import 'dart:convert';
import 'dart:isolate';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:project/features/auth/views/login/notification_page.dart';
import 'package:provider/provider.dart';
import 'package:project/core/providers/app_state_provider.dart';
import 'package:project/core/providers/map_state_provider.dart';
import 'package:project/core/providers/ride_state_provider.dart';
import 'package:project/core/providers/suggestion_provider.dart';
import 'package:project/core/services/socket_service.dart';
import 'package:project/core/services/ride_service.dart';
import 'package:project/core/services/voice_assistant_service.dart';
import 'package:project/features/driver/views/nearby_drivers_screen.dart';
import 'package:project/features/suggestions/screens/suggestions_screen.dart';
import 'package:project/features/suggestions/widgets/suggestion_button.dart';
import 'package:project/features/voice_assistant/voice_assistant_screen.dart';
import 'package:project/features/voice_assistant/widgets/voice_command_button.dart';
import 'package:shared_preferences/shared_preferences.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final Completer<GoogleMapController> _mapController = Completer();
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  LatLng? _currentPosition;
  final LatLng _defaultPosition = const LatLng(37.7749, -122.4194);
  Set<Marker> _markers = {};
  List<LatLng> _polylinePoints = [];
  Set<Polyline> _polylines = {};
  bool _isMapInitialized = false;
  LatLng? _carPosition;
  List<LatLng> _routePoints = [];
  late TextEditingController toController;
  bool _rideCompleted = false;
  final RideService _rideService = RideService.instance;
  final VoiceAssistantService _voiceAssistantService = VoiceAssistantService();

  String? _currentRideId;
  String? _currentRideStatus;
  String? _currentDriverId;
  Map<String, dynamic>? _currentDriverInfo;
  bool _isSharedRide = false;
  final List<Function()> _activeSubscriptions = [];

  @override
  void initState() {
    super.initState();
    toController = TextEditingController();
    _initializeWithErrorHandling();
  }

  Future<void> _initializeWithErrorHandling() async {
    try {
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        await _getCurrentLocation();
      });
      SocketService.instance.connect();
      _listenForRideSharingOpportunities();
      await _initializeVoiceAssistant();
    } catch (e) {
      debugPrint('Initialization error: $e');
      if (mounted) {
        setState(() {
          _currentPosition = _defaultPosition;
          _markers.add(
            Marker(
              markerId: const MarkerId('default_location'),
              position: _defaultPosition,
              infoWindow: const InfoWindow(title: 'Default Location'),
            ),
          );
        });
      }
    }
  }

  Future<void> _initializeVoiceAssistant() async {
    try {
      await _voiceAssistantService.initialize();
    } catch (e) {
      debugPrint('Voice assistant initialization error: $e');
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location services are disabled')),
          );
          setState(() {
            _currentPosition = _defaultPosition;
            _markers.add(
              Marker(
                markerId: const MarkerId('default_location'),
                position: _defaultPosition,
                infoWindow: const InfoWindow(title: 'Default Location'),
              ),
            );
          });
        }
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Location permissions denied')),
            );
            setState(() {
              _currentPosition = _defaultPosition;
              _markers.add(
                Marker(
                  markerId: const MarkerId('default_location'),
                  position: _defaultPosition,
                  infoWindow: const InfoWindow(title: 'Default Location'),
                ),
              );
            });
          }
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Location permissions permanently denied')),
          );
          setState(() {
            _currentPosition = _defaultPosition;
            _markers.add(
              Marker(
                markerId: const MarkerId('default_location'),
                position: _defaultPosition,
                infoWindow: const InfoWindow(title: 'Default Location'),
              ),
            );
          });
        }
        return;
      }

      Position position = await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
        ),
      );

      if (mounted) {
        setState(() {
          _currentPosition = LatLng(position.latitude, position.longitude);
          _markers.add(
            Marker(
              markerId: const MarkerId('current_location'),
              position: _currentPosition!,
              infoWindow: const InfoWindow(
                title: 'Your Location',
                snippet: 'You are here',
              ),
              icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
            ),
          );
        });

        if (_isMapInitialized && _mapController.isCompleted) {
          final controller = await _mapController.future;
          controller.animateCamera(
            CameraUpdate.newLatLngZoom(_currentPosition!, 16.0),
          );
        }
      }
    } catch (e) {
      debugPrint('Error getting current location: $e');
      if (mounted) {
        setState(() {
          _currentPosition = _defaultPosition;
          _markers.add(
            Marker(
              markerId: const MarkerId('default_location'),
              position: _defaultPosition,
              infoWindow: const InfoWindow(title: 'Default Location'),
            ),
          );
        });
      }
    }
  }

  void _listenForRideSharingOpportunities() {
    final sharingOpportunitySubscription = SocketService.instance.listenTo(
      'ride:sharing_opportunity',
      (data) {
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('A ride sharing opportunity is available nearby!'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'View',
              onPressed: () {
                _showRideSharingOpportunityDialog(data);
              },
            ),
          ),
        );
      },
    );
    _activeSubscriptions.add(() => sharingOpportunitySubscription.cancel());
  }

  void _showRideSharingOpportunityDialog(dynamic data) {
    if (!mounted) return;
    final activeRideId = data['activeRideId'];
    final pickupLocation = data['pickupLocation'];
    final destination = data['destination'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.share, color: Colors.green),
            SizedBox(width: 8),
            Text('Ride Sharing Opportunity'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('A ride is available for sharing nearby. Would you like to join?'),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Row(
                    children: [
                      Icon(Icons.location_on, color: Colors.green, size: 20),
                      SizedBox(width: 8),
                      Text('Pickup Point:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text('${pickupLocation['lat'].toStringAsFixed(4)}, ${pickupLocation['lng'].toStringAsFixed(4)}'),
                  const SizedBox(height: 8),
                  const Row(
                    children: [
                      Icon(Icons.location_pin, color: Colors.red, size: 20),
                      SizedBox(width: 8),
                      Text('Destination:', style: TextStyle(fontWeight: FontWeight.bold)),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text('${destination['lat'].toStringAsFixed(4)}, ${destination['lng'].toStringAsFixed(4)}'),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _requestRideSharing(activeRideId);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              foregroundColor: Colors.white,
            ),
            child: const Text('Request to Join'),
          ),
        ],
      ),
    );
  }

  void _requestRideSharing(String activeRideId) async {
    if (!mounted) return;
    try {
      _showLoadingDialog('Sending ride sharing request...');
      final riderId = await _getUserId();
      if (_currentPosition == null) {
        if (mounted) {
          Navigator.of(context, rootNavigator: true).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Current location not found'), backgroundColor: Colors.red),
          );
        }
        return;
      }

      SocketService.instance.socket?.emit('ride:sharing_request', {
        'rideId': activeRideId,
        'newRiderId': riderId,
        'newPickupLocation': {'lat': _currentPosition!.latitude, 'lng': _currentPosition!.longitude},
        'newDestination': _markers.isNotEmpty
            ? {'lat': _markers.first.position.latitude, 'lng': _markers.first.position.longitude}
            : {'lat': _currentPosition!.latitude + 0.01, 'lng': _currentPosition!.longitude + 0.01},
      });

      late final StreamSubscription responseSubscription;
      responseSubscription = SocketService.instance.listenTo(
        'ride:sharing_requested',
        (response) {
          if (mounted) {
            Navigator.of(context, rootNavigator: true).pop();
            if (response['success'] == true) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Ride sharing request sent successfully'), backgroundColor: Colors.green),
              );
              _showPredictionDetailsDialog(response['prediction']);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Failed to send ride sharing request: ${response['message']}'), backgroundColor: Colors.red),
              );
            }
            responseSubscription.cancel();
          }
        },
      );
      _activeSubscriptions.add(() => responseSubscription.cancel());
    } catch (e) {
      debugPrint('Ride sharing request error: $e');
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Ride sharing request error: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<String> _getUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = prefs.getString('user_id') ?? 'user_${DateTime.now().millisecondsSinceEpoch}';
      await prefs.setString('user_id', userId);
      return userId;
    } catch (e) {
      debugPrint('Error getting user ID: $e');
      return 'user_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  void _showLoadingDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(message),
            ],
          ),
        ),
      ),
    );
  }

  void _showPredictionDetailsDialog(dynamic prediction) {
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.analytics, color: Colors.blue),
            SizedBox(width: 8),
            Text('AI Analysis'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text('AI analysis for ride sharing request:'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          prediction['shouldAddRider'] ? Icons.check_circle : Icons.cancel,
                          color: prediction['shouldAddRider'] ? Colors.green : Colors.red,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          prediction['shouldAddRider'] ? 'Ride sharing beneficial' : 'Ride sharing not beneficial',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text('Confidence score: ${(prediction['score'] * 100).toStringAsFixed(1)}%'),
                    Text('Sharing efficiency: ${prediction['efficiency']}%'),
                    if (prediction['fareDetails'] != null) ...[
                      const SizedBox(height: 16),
                      const Text('Fare details:', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Text('Total savings: ${prediction['fareDetails']['totalSavings']} EGP'),
                      Text('Original rider savings: ${prediction['fareDetails']['originalRider']['savings']} EGP'),
                      Text('New rider savings: ${prediction['fareDetails']['newRider']['savings']} EGP'),
                    ],
                    if (prediction['environmentalImpact'] != null) ...[
                      const SizedBox(height: 16),
                      const Text('Environmental impact:', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Text('CO2 reduction: ${prediction['environmentalImpact']['co2Reduction']} kg'),
                      Text('Fuel saved: ${prediction['environmentalImpact']['fuelSaved']} liters'),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    toController.dispose();
    for (final disposeFunction in _activeSubscriptions) {
      disposeFunction();
    }
    _activeSubscriptions.clear();
    super.dispose();
  }

  void _showVoiceAssistantOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.mic),
            title: const Text('Use Voice Assistant'),
            onTap: () {
              Navigator.pop(context);
              _startVoiceAssistant();
            },
          ),
          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('Open Voice Assistant Screen'),
            onTap: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const VoiceAssistantScreen()),
              );
            },
          ),
        ],
      ),
    );
  }

  Future<void> _loadPersonalizedSuggestions() async {
    try {
      final userId = await _getUserId();
      if (!mounted) return;
      final suggestionProvider = Provider.of<SuggestionProvider>(context, listen: false);
      await suggestionProvider.generatePersonalizedSuggestions(userId);
      if (_currentPosition != null) {
        await suggestionProvider.loadSuggestionsForLocation(_currentPosition!, userId: userId);
      }
      debugPrint('Loaded ${suggestionProvider.filteredSuggestions.length} suggestions');
    } catch (e) {
      debugPrint('Error loading personalized suggestions: $e');
    }
  }

  Future<void> _loadAndShowSuggestions() async {
    try {
      final suggestionProvider = Provider.of<SuggestionProvider>(context, listen: false);
      if (_currentPosition != null) {
        final userId = await _getUserId();
        await suggestionProvider.loadSuggestionsForLocation(_currentPosition!, userId: userId);
        if (mounted) {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const SuggestionsScreen()),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please enable location services to get suggestions')),
          );
          _getCurrentLocation();
        }
      }
    } catch (e) {
      debugPrint('Error loading suggestions: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading suggestions: $e')),
        );
      }
    }
  }

  Future<void> _startVoiceAssistant() async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Listening...'), backgroundColor: Colors.green, duration: Duration(seconds: 2)),
    );
    final command = await _voiceAssistantService.listen();
    if (command != null && command.isNotEmpty) {
      await _voiceAssistantService.processCommand(command);
      if (mounted) {
        if (command.contains('request ride')) {
          _showAddressPopup(context);
        } else if (command.contains('where is the driver')) {
          _checkDriverLocation();
        } else if (command.contains('cancel ride')) {
          _cancelRide();
        } else if (command.contains('confirm ride') || command.contains('start ride')) {
          _confirmRideStart();
        } else if (command.contains('share ride')) {
          _showRideSharingOptions();
        } else if (command.contains('home page')) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('You are already on the home page'), backgroundColor: Colors.blue),
          );
        }
      }
    }
  }

  Future<void> _checkDriverLocation() async {
    try {
      final hasActiveRide = _markers.any((m) => m.markerId.value == 'driver');
      if (!hasActiveRide) {
        await _voiceAssistantService.speak('You don’t have an active ride currently.');
        return;
      }
      if (_carPosition != null) {
        await _voiceAssistantService.speak('The driver is on the way to you and will arrive soon.');
        if (_mapController.isCompleted) {
          final controller = await _mapController.future;
          controller.animateCamera(CameraUpdate.newLatLng(_carPosition!));
        }
      } else {
        await _voiceAssistantService.speak('Searching for a driver. Please wait...');
      }
    } catch (e) {
      debugPrint('Error checking driver location: $e');
      await _voiceAssistantService.speak('An error occurred while checking the driver’s location.');
    }
  }

  Future<void> _confirmRideStart() async {
    try {
      final hasActiveRide = _markers.any((m) => m.markerId.value == 'driver');
      if (!hasActiveRide) {
        await _voiceAssistantService.speak('You don’t have an active ride to start.');
        return;
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Ride start confirmed'), backgroundColor: Colors.green),
        );
      }
      await _voiceAssistantService.speak('Ride start confirmed. Have a safe trip!');
    } catch (e) {
      debugPrint('Error confirming ride start: $e');
      await _voiceAssistantService.speak('An error occurred while confirming the ride start.');
    }
  }

  void _showRideSharingOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share Ride Details'),
            onTap: () {
              Navigator.pop(context);
              _voiceAssistantService.speak('Sharing ride details...');
            },
          ),
          ListTile(
            leading: const Icon(Icons.group_add),
            title: const Text('Search for Ride Sharing Opportunities'),
            onTap: () {
              Navigator.pop(context);
              _voiceAssistantService.speak('Searching for ride sharing opportunities...');
            },
          ),
        ],
      ),
    );
  }

  Future<void> _requestRide() async {
    try {
      await _voiceAssistantService.speak('Requesting a new ride...');
      await Future.delayed(const Duration(seconds: 2));
      await _voiceAssistantService.speak('Ride requested successfully. Searching for a driver...');
      final driverPosition = LatLng(_currentPosition!.latitude + 0.01, _currentPosition!.longitude + 0.01);
      setState(() {
        _markers.add(
          Marker(
            markerId: const MarkerId('driver'),
            position: driverPosition,
            icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
            infoWindow: const InfoWindow(title: 'Driver', snippet: 'On the way to you'),
          ),
        );
        _carPosition = driverPosition;
      });
    } catch (e) {
      debugPrint('Error requesting ride: $e');
      await _voiceAssistantService.speak('An error occurred while requesting the ride.');
    }
  }

  void _showCancelRideConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Ride'),
        content: const Text('Are you sure you want to cancel the current ride?'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _voiceAssistantService.speak('Cancellation aborted.');
            },
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _cancelRide();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
            child: const Text('Yes, Cancel Ride'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelRide() async {
    try {
      final hasActiveRide = _markers.any((m) => m.markerId.value == 'driver');
      if (!hasActiveRide) {
        await _voiceAssistantService.speak('You don’t have an active ride to cancel.');
        return;
      }
      await Future.delayed(const Duration(seconds: 1));
      setState(() {
        _markers.removeWhere((m) => m.markerId.value == 'driver');
        _polylines.clear();
        _carPosition = null;
      });
      await _voiceAssistantService.speak('Ride cancelled successfully.');
    } catch (e) {
      debugPrint('Error cancelling ride: $e');
      await _voiceAssistantService.speak('An error occurred while cancelling the ride.');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: const Drawer(),
      body: _buildFlutterMapPage(),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.only(left: 16.0, bottom: 16.0),
        child: Align(
          alignment: Alignment.bottomLeft,
          child: VoiceCommandButton(
            position: VoiceButtonPosition.bottomLeft,
            onSetDestination: (location, address) {
              if (_currentPosition != null) {
                _drawRouteAndAnimate(_currentPosition!, location);
              }
            },
            onRequestRide: _requestRide,
            onCheckStatus: _checkDriverLocation,
            onCancelRide: _showCancelRideConfirmation,
            onNavigateToHome: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('You are already on the home page'), backgroundColor: Colors.blue),
              );
            },
            onNavigateBack: () => Navigator.of(context).maybePop(),
          ),
        ),
      ),
    );
  }

  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> points = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      points.add(LatLng(lat / 1E5, lng / 1E5));
    }
    debugPrint('Decoded ${points.length} points from polyline');
    return points;
  }

  Future<void> _drawRouteAndAnimate(LatLng start, LatLng end) async {
    if (!_isMapInitialized) {
      debugPrint('Map not initialized yet');
      return;
    }

    try {
      debugPrint('Drawing route from $start to $end');
      final mapProvider = Provider.of<MapStateProvider>(context, listen: false);
      final rideProvider = Provider.of<RideStateProvider>(context, listen: false);

      rideProvider.updatePickupAndDestination(start, end);

      final String apiKey = dotenv.env['GOOGLE_MAPS_API_KEY'] ?? '';
      if (apiKey.isEmpty) {
        throw Exception('Google Maps API key is missing');
      }

      final String url = 'https://maps.googleapis.com/maps/api/directions/json'
          '?origin=${start.latitude},${start.longitude}'
          '&destination=${end.latitude},${end.longitude}'
          '&mode=driving'
          '&key=$apiKey';

      final response = await http.get(Uri.parse(url));
      debugPrint('Google Directions API Response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data['status'] != 'OK') {
          throw Exception('Directions API error: ${data['status']} - ${data['error_message'] ?? 'No error message'}');
        }

        if (data['routes'] != null && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final encodedPolyline = route['overview_polyline']['points'];
          final points = _decodePolyline(encodedPolyline);

          mapProvider.setRoutePoints(points);
          mapProvider.clearPolylines();
          mapProvider.addPolyline(
            Polyline(
              polylineId: const PolylineId('route'),
              points: points,
              color: Colors.blue,
              width: 4,
            ),
          );

          setState(() {
            _routePoints = points;
            _polylinePoints = points;
            _polylines.clear();
            _polylines.add(
              Polyline(
                polylineId: const PolylineId('route'),
                points: points,
                color: Colors.blue,
                width: 4,
              ),
            );
          });

          if (_mapController.isCompleted) {
            final controller = await _mapController.future;
            controller.animateCamera(
              CameraUpdate.newLatLngBounds(
                LatLngBounds(
                  southwest: LatLng(min(start.latitude, end.latitude) - 0.05, min(start.longitude, end.longitude) - 0.05),
                  northeast: LatLng(max(start.latitude, end.latitude) + 0.05, max(start.longitude, end.longitude) + 0.05),
                ),
                100,
              ),
            );
          }

          _startCarAnimation();
        } else {
          throw Exception('No routes found');
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.reasonPhrase}');
      }
    } catch (e) {
      debugPrint('Error drawing route: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to draw route: $e')),
        );
      }
    }
  }

  void _startCarAnimation() async {
    if (_routePoints.isEmpty) return;

    final receivePort = ReceivePort();
    await Isolate.spawn(
      _animateCarIsolate,
      {
        'sendPort': receivePort.sendPort,
        'routePoints': _routePoints.map((point) => {'lat': point.latitude, 'lng': point.longitude}).toList(),
      },
    );

    receivePort.listen((message) {
      if (message is int && message < _routePoints.length) {
        setState(() {
          _carPosition = _routePoints[message];
          _markers.removeWhere((m) => m.markerId.value == 'car');
          _markers.add(
            Marker(
              markerId: const MarkerId('car'),
              position: _carPosition!,
              icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
            ),
          );
          if (_mapController.isCompleted) {
            _mapController.future.then((controller) {
              controller.animateCamera(CameraUpdate.newLatLng(_carPosition!));
            });
          }
        });
      } else if (message == _routePoints.length) {
        setState(() => _rideCompleted = true);
        receivePort.close();
      }
    });
  }

  static void _animateCarIsolate(Map<String, dynamic> params) async {
    final SendPort sendPort = params['sendPort'];
    final List<Map<String, dynamic>> routePoints = params['routePoints'];
    int currentIndex = 0;
    while (true) {
      await Future.delayed(const Duration(milliseconds: 500));
      if (currentIndex < routePoints.length) {
        sendPort.send(currentIndex);
        currentIndex++;
      } else {
        sendPort.send(routePoints.length);
        break;
      }
    }
  }

  void _onMapTap(LatLng position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(position.latitude, position.longitude);
      if (placemarks.isNotEmpty) {
        Placemark placemark = placemarks.first;
        String address = "${placemark.name ?? ''}, ${placemark.locality ?? ''}, ${placemark.country ?? ''}";
        setState(() {
          _markers.clear();
          _markers.add(
            Marker(
              markerId: const MarkerId('selected_location'),
              position: position,
              icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
            ),
          );
        });
        if (mounted) {
          _showAddressPopup(context, toAddress: address, toPosition: position);
        }
      }
    } catch (e) {
      debugPrint('Error reverse geocoding: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error reverse geocoding: $e')),
        );
      }
    }
  }

  Future<void> _searchLocation(String address) async {
    try {
      final mapProvider = Provider.of<MapStateProvider>(context, listen: false);
      await mapProvider.searchLocation(address);
      if (mapProvider.markers.isNotEmpty) {
        setState(() {
          _markers.clear();
          _markers.addAll(mapProvider.markers);
        });
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Location not found')),
        );
      }
    } catch (e) {
      debugPrint('Error searching location: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error searching location: $e')),
        );
      }
    }
  }

  void _updateMarkers(Set<Marker> newMarkers) {
    setState(() => _markers = newMarkers);
  }

  void _refreshMap() {
    setState(() {
      _markers.clear();
      _polylinePoints.clear();
      _routePoints.clear();
      _polylines.clear();
      _carPosition = null;
      _rideCompleted = false;
      toController.clear();
      if (_currentPosition != null) {
        _markers.add(
          Marker(
            markerId: const MarkerId('current_location'),
            position: _currentPosition!,
            icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
          ),
        );
        if (_isMapInitialized && _mapController.isCompleted) {
          _mapController.future.then((controller) {
            controller.animateCamera(CameraUpdate.newLatLngZoom(_currentPosition!, 14.0));
          });
        }
      }
    });
  }

  Widget _buildFlutterMapPage() {
    if (!_isMapInitialized || _currentPosition == null) {
      return const Center(child: CircularProgressIndicator());
    }
    return Stack(
      children: [
        GoogleMap(
          initialCameraPosition: CameraPosition(
            target: _currentPosition ?? _defaultPosition,
            zoom: 16.0,
          ),
          myLocationEnabled: true,
          myLocationButtonEnabled: false,
          markers: _markers,
          polylines: _polylines,
          onMapCreated: (GoogleMapController controller) {
            _mapController.complete(controller);
            setState(() => _isMapInitialized = true);
            if (_currentPosition != null) {
              controller.animateCamera(CameraUpdate.newLatLngZoom(_currentPosition!, 16.0));
            }
          },
          onTap: _onMapTap,
        ),
        Positioned(
          top: 40,
          left: 16,
          child: OutlinedButton(
            onPressed: () => _scaffoldKey.currentState?.openDrawer(),
            child: const Icon(Icons.menu, size: 28, color: Colors.black),
          ),
        ),
        Positioned(
          top: 40,
          right: 16,
          child: Row(
            children: [
              OutlinedButton(
                onPressed: _loadAndShowSuggestions,
                style: OutlinedButton.styleFrom(backgroundColor: Colors.white),
                child: const Icon(Icons.lightbulb, size: 28, color: Colors.grey),
              ),
              const SizedBox(width: 8),
              OutlinedButton(
                onPressed: () => Navigator.of(context).push(
                  MaterialPageRoute(builder: (context) => const NotificationPage()),
                ),
                child: const Icon(Icons.notifications_none, size: 28, color: Colors.black),
              ),
              const SizedBox(width: 8),
              OutlinedButton(
                onPressed: () {
                  if (_currentPosition != null) {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => NearbyDriversScreen(
                          userLocation: _currentPosition!,
                          searchRadius: 5.0,
                        ),
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Current location not available')),
                    );
                  }
                },
                child: const Icon(Icons.local_taxi, size: 28, color: Colors.black),
              ),
            ],
          ),
        ),
        Positioned(
          bottom: 140,
          left: 20,
          right: 20,
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: toController,
                  decoration: const InputDecoration(
                    fillColor: Colors.white70,
                    filled: true,
                    prefixIcon: Icon(Icons.location_on_outlined),
                    labelText: 'Where would you go?',
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (value) => setState(() {}),
                  onSubmitted: (value) {
                    if (toController.text.isNotEmpty) {
                      _searchLocation(toController.text);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              CircleAvatar(
                radius: 24,
                backgroundColor: Colors.white70,
                child: IconButton(
                  onPressed: _getCurrentLocation,
                  icon: const Icon(Icons.location_on, color: Colors.black, size: 30),
                ),
              ),
            ],
          ),
        ),
        if (_rideCompleted)
          Positioned(
            bottom: 40,
            left: 20,
            right: 20,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              ),
              onPressed: _refreshMap,
              child: const Text("Refresh and Start Over", style: TextStyle(color: Colors.white)),
            ),
          ),
      ],
    );
  }

  void _showAddressPopup(
    BuildContext context, {
    String? toAddress,
    LatLng? toPosition,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(16))),
      builder: (context) => AddressPopup(
        currentPosition: _currentPosition,
        onSearch: _searchLocation,
        onRouteConfirmed: _drawRouteAndAnimate,
        onSendRideRequest: _sendRideRequest,
        initialToAddress: toAddress,
        initialToPosition: toPosition,
      ),
    );
  }

  Future<void> _sendRideRequest(LatLng destination) async {
    try {
      if (!mounted) return;
      await _showAvailableDriversDialog(destination);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sending ride request: $e')),
        );
      }
    }
  }

  Future<void> _showAvailableDriversDialog(LatLng destination) async {
    try {
      _showLoadingDialog('Searching for available drivers...');
      final userId = await _getUserId();
      final ride = await _rideService.createRide(
        riderId: userId,
        pickupLocation: {'lat': _currentPosition?.latitude ?? 0, 'lng': _currentPosition?.longitude ?? 0},
        destination: {'lat': destination.latitude, 'lng': destination.longitude},
      );
      debugPrint('Ride created successfully with ID: ${ride.id}');
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      final List<Map<String, dynamic>> availableDrivers = [
        {
          'id': 'driver_1',
          'name': 'Ahmed Mohamed',
          'rating': 4.8,
          'car': 'Toyota Corolla',
          'plate': 'ABC 123',
          'distance': '3 km',
          'eta': '5 min',
          'price': '25 EGP',
        },
        {
          'id': 'driver_2',
          'name': 'Mahmoud Ali',
          'rating': 4.5,
          'car': 'Hyundai Elantra',
          'plate': 'XYZ 789',
          'distance': '4 km',
          'eta': '7 min',
          'price': '23 EGP',
        },
        {
          'id': 'driver_3',
          'name': 'Khaled Ibrahim',
          'rating': 4.9,
          'car': 'Nissan Sunny',
          'plate': 'DEF 456',
          'distance': '2 km',
          'eta': '3 min',
          'price': '28 EGP',
        },
      ];

      if (mounted) {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
          builder: (context) => Container(
            padding: const EdgeInsets.all(20),
            height: MediaQuery.of(context).size.height * 0.7,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Choose a Driver', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                const SizedBox(height: 5),
                const Text('Available drivers near you', style: TextStyle(fontSize: 16, color: Colors.grey)),
                const SizedBox(height: 20),
                Expanded(
                  child: ListView.separated(
                    itemCount: availableDrivers.length,
                    separatorBuilder: (context, index) => const Divider(),
                    itemBuilder: (context, index) {
                      final driver = availableDrivers[index];
                      return InkWell(
                        onTap: () {
                          Navigator.pop(context);
                          _requestSpecificDriver(ride.id, driver['id'], destination);
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 25,
                                backgroundColor: Colors.grey.shade200,
                                child: Icon(Icons.person, color: Colors.grey.shade700, size: 30),
                              ),
                              const SizedBox(width: 15),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(driver['name'], style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                                        Text(driver['price'], style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                                      ],
                                    ),
                                    const SizedBox(height: 5),
                                    Row(
                                      children: [
                                        const Icon(Icons.star, color: Colors.amber, size: 16),
                                        Text(' ${driver['rating']}'),
                                        const SizedBox(width: 10),
                                        Text('${driver['car']} • ${driver['plate']}'),
                                      ],
                                    ),
                                    const SizedBox(height: 5),
                                    Row(
                                      children: [
                                        const Icon(Icons.location_on, color: Colors.green, size: 16),
                                        Text(' ${driver['distance']}'),
                                        const SizedBox(width: 10),
                                        const Icon(Icons.access_time, color: Colors.orange, size: 16),
                                        Text(' ${driver['eta']}'),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      }
      _setupRideListeners(ride.id);
    } catch (e) {
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error searching for drivers: $e'), backgroundColor: Colors.red),
        );
      }
      rethrow;
    }
  }

  Future<void> _requestSpecificDriver(String rideId, String driverId, LatLng destination) async {
    try {
      if (!mounted) return;
      _showWaitingForDriverPopup(context);
      final userId = await _getUserId();
      SocketService.instance.socket?.emit('ride:request', {
        'rideId': rideId,
        'riderId': userId,
        'driverId': driverId,
        'pickupLocation': {'lat': _currentPosition?.latitude ?? _defaultPosition.latitude, 'lng': _currentPosition?.longitude ?? _defaultPosition.longitude},
        'destination': {'lat': destination.latitude, 'lng': destination.longitude},
      });
    } catch (e) {
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error sending ride request: $e')),
        );
      }
    }
  }

  void _setupRideListeners(String? rideId) {
    final acceptSubscription = SocketService.instance.onRideAccepted.listen((data) {
      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (e) {
          debugPrint('Error closing waiting dialog: $e');
        }
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Ride request accepted! Preparing ride...'), backgroundColor: Colors.green, duration: Duration(seconds: 3)),
        );
        _handleRideAccepted(data);
      }
    });

    final sharedRideSubscription = SocketService.instance.onRideShared.listen((data) {
      if (mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (e) {
          debugPrint('Error closing waiting dialog: $e');
        }
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Your ride will be shared with another rider! Preparing ride...'), backgroundColor: Colors.green, duration: Duration(seconds: 3)),
        );
        _handleRideShared(data);
      }
    });

    final rejectSubscription = SocketService.instance.onRideRejected.listen((data) {
      if (mounted) {
        Navigator.pop(context);
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Request Rejected'),
            content: const Text('Unfortunately, the driver rejected your ride request. You can try again or explore other options.'),
            actions: [
              TextButton(onPressed: () => Navigator.pop(context), child: const Text('OK')),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  if (_markers.isNotEmpty) _sendRideRequest(_markers.first.position);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.green),
                child: const Text('Retry'),
              ),
            ],
          ),
        );
      }
    });

    final driverUnavailableSubscription = SocketService.instance.onDriverUnavailable.listen((data) {
      if (mounted) {
        try {
          Navigator.pop(context);
        } catch (e) {
          debugPrint('Error closing dialog: $e');
        }
        _handleDriverUnavailable(data);
      }
    });

    final noDriversAvailableSubscription = SocketService.instance.onNoDriversAvailable.listen((data) {
      if (mounted) {
        try {
          Navigator.pop(context);
        } catch (e) {
          debugPrint('Error closing dialog: $e');
        }
        _handleNoDriversAvailable(data);
      }
    });

    final driverArrivedAtPickupSubscription = SocketService.instance.onDriverArrivedAtPickup.listen((data) {
      if (mounted) _handleDriverArrivedAtPickup(data);
    });

    final driverArrivedAtDestinationSubscription = SocketService.instance.onDriverArrivedAtDestination.listen((data) {
      if (mounted) _handleDriverArrivedAtDestination(data);
    });

    final rideErrorSubscription = SocketService.instance.onRideError.listen((data) {
      if (mounted) _handleRideError(data);
    });

    final rideStartedSubscription = SocketService.instance.onRideStarted.listen((data) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Ride started! Driver is on the way to the destination...'), backgroundColor: Colors.green, duration: Duration(seconds: 3)),
        );
        _handleRideStarted(data);
      }
    });

    Timer? timeoutTimer;
    if (rideId != null) {
      timeoutTimer = Timer(const Duration(minutes: 2), () async {
        if (!mounted) return;
        final ride = await _rideService.getRideById(rideId);
        if (ride.status == 'pending' && mounted) {
          await _rideService.updateRide(rideId, {'status': 'cancelled'});
          try {
            Navigator.of(context, rootNavigator: true).pop();
          } catch (e) {
            debugPrint('Error closing dialog: $e');
          }
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('Waiting Timeout'),
              content: const Text('No available driver was found within the time limit. Please try again later.'),
              actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('OK'))],
            ),
          );
        }
      });
    }

    void disposeListeners() {
      acceptSubscription.cancel();
      rejectSubscription.cancel();
      driverUnavailableSubscription.cancel();
      noDriversAvailableSubscription.cancel();
      driverArrivedAtPickupSubscription.cancel();
      driverArrivedAtDestinationSubscription.cancel();
      rideErrorSubscription.cancel();
      rideStartedSubscription.cancel();
      sharedRideSubscription.cancel();
      timeoutTimer?.cancel();
    }

    _activeSubscriptions.add(disposeListeners);
  }

  void _showWaitingForDriverPopup(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PopScope(
        canPop: false,
        child: Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          child: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 10),
                Container(
                  padding: const EdgeInsets.all(15),
                  decoration: BoxDecoration(color: Colors.green.withOpacity(0.2), shape: BoxShape.circle),
                  child: const CircularProgressIndicator(valueColor: AlwaysStoppedAnimation<Color>(Colors.green), strokeWidth: 3),
                ),
                const SizedBox(height: 20),
                const Text('Waiting for Driver', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                const SizedBox(height: 15),
                const Text('Searching for available drivers...', textAlign: TextAlign.center, style: TextStyle(fontSize: 16)),
                const SizedBox(height: 10),
                StreamBuilder<int>(
                  stream: Stream.periodic(const Duration(seconds: 1), (i) => i),
                  builder: (context, snapshot) => Text('Waiting time: ${snapshot.data ?? 0} seconds', style: const TextStyle(fontSize: 14, color: Colors.grey)),
                ),
                const SizedBox(height: 25),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Ride request cancelled')));
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
                  ),
                  child: const Text('Cancel Request', style: TextStyle(fontSize: 16)),
                ),
                const SizedBox(height: 10),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _startRide({
    required LatLng driverPosition,
    required LatLng pickupLocation,
    required LatLng destination,
    required String rideId,
    required String driverId,
  }) async {
    setState(() {
      _markers.clear();
      _polylines.clear();
      _markers.add(Marker(markerId: const MarkerId('driver'), position: driverPosition, icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure)));
      _markers.add(Marker(markerId: const MarkerId('pickup'), position: pickupLocation, icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen)));
      _markers.add(Marker(markerId: const MarkerId('destination'), position: destination, icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed)));
    });

    await _drawRouteAndAnimate(driverPosition, pickupLocation);
    _listenForDriverLocationUpdates(driverId);

    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Driver has arrived at pickup')));
        _drawRouteAndAnimate(pickupLocation, destination);
        _rideService.updateRide(rideId, {'status': 'in_progress'});
      }
    });
  }

  void _startSimpleRide() {
    if (_currentPosition != null && _markers.isNotEmpty) {
      _drawRouteAndAnimate(_currentPosition!, _markers.first.position);
    }
  }

  void _startRideInBackground({
    required LatLng driverPosition,
    required LatLng pickupLocation,
    required LatLng destination,
    required String rideId,
    required String driverId,
  }) {
    debugPrint('Starting ride in background: rideId=$rideId, driverId=$driverId');
    _rideService.updateRide(rideId, {'status': 'accepted'});
    _listenForDriverLocationUpdates(driverId);
    Future.delayed(const Duration(seconds: 10), () {
      if (mounted) {
        debugPrint('Driver arrived at pickup location');
        _rideService.updateRide(rideId, {'status': 'in_progress'});
      }
    });
  }

  void _listenForDriverLocationUpdates(String driverId) {
    SocketService.instance.onDriverLocationUpdate.listen((data) {
      if (data['driverId'] == driverId && mounted) {
        try {
          final lat = data['location']['lat'];
          final lng = data['location']['lng'];
          final phase = data['phase'] as String?;
          final progress = data['progress'] as Map<String, dynamic>?;

          setState(() {
            _carPosition = LatLng(lat, lng);
            _markers.removeWhere((m) => m.markerId.value == 'driver');
            _markers.add(
              Marker(
                markerId: const MarkerId('driver'),
                position: _carPosition!,
                icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
                infoWindow: InfoWindow(title: 'Driver', snippet: phase == 'pickup' ? 'On the way to you' : 'On the way to destination'),
              ),
            );
            if (progress != null) {
              final percentage = progress['percentage'] as int? ?? 0;
              _updateProgressIndicator(percentage, phase ?? 'pickup');
            }
          });

          if (_mapController.isCompleted) {
            _mapController.future.then((controller) => controller.animateCamera(CameraUpdate.newLatLng(_carPosition!)));
          }
        } catch (e) {
          debugPrint('Error updating driver location: $e');
        }
      }
    });
  }

  void _updateProgressIndicator(int percentage, String phase) {
    if (mounted) {
      ScaffoldMessenger.of(context).clearSnackBars();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(phase == 'pickup' ? 'Driver on the way to you: $percentage%' : 'On the way to destination: $percentage%'),
          duration: const Duration(seconds: 1),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _handleDriverUnavailable(Map<String, dynamic> data) {
    final String driverId = data['driverId'] ?? '';
    final String message = data['message'] ?? 'The selected driver is currently unavailable';
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(children: [Icon(Icons.error_outline, color: Colors.orange.shade700), const SizedBox(width: 10), const Text('Driver Unavailable')]),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [Text(message), const SizedBox(height: 10), Text('Driver ID: $driverId', style: const TextStyle(fontSize: 14, color: Colors.grey))],
          ),
          actions: [
            TextButton(onPressed: () => Navigator.pop(context), child: const Text('Cancel')),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                if (_markers.isNotEmpty) _showAvailableDriversDialog(_markers.first.position);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
              child: const Text('Choose Another Driver'),
            ),
          ],
        ),
      );
    }
  }

  void _handleNoDriversAvailable(Map<String, dynamic> data) {
    final String message = data['message'] ?? 'No drivers available currently. Please try again later.';
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(children: [Icon(Icons.error_outline, color: Colors.red.shade700), const SizedBox(width: 10), const Text('No Drivers Available')]),
          content: Text(message),
          actions: [
            TextButton(onPressed: () => Navigator.pop(context), child: const Text('OK')),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                if (_markers.isNotEmpty) _sendRideRequest(_markers.first.position);
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }
  }

  void _handleDriverArrivedAtPickup(Map<String, dynamic> data) {
    final String message = data['message'] ?? 'Driver has arrived at pickup';
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.green, duration: const Duration(seconds: 5), action: SnackBarAction(label: 'OK', textColor: Colors.white, onPressed: () {})),
      );
    }
  }

  void _handleDriverArrivedAtDestination(Map<String, dynamic> data) {
    final String message = data['message'] ?? 'Driver has arrived at destination';
    final location = data['location'] != null ? LatLng(data['location']['lat'], data['location']['lng']) : null;
    if (mounted) {
      if (location != null) {
        setState(() {
          _carPosition = location;
          _markers.removeWhere((m) => m.markerId.value == 'driver');
          _markers.add(
            Marker(
              markerId: const MarkerId('driver'),
              position: _carPosition!,
              icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
              infoWindow: const InfoWindow(title: 'Driver', snippet: 'Arrived at destination'),
            ),
          );
        });
      }
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(children: [Icon(Icons.check_circle, color: Colors.green), SizedBox(width: 10), Text('Ride Completed')]),
          content: Column(mainAxisSize: MainAxisSize.min, children: [Text(message), const SizedBox(height: 20), const Text('Thank you for using our app!')]),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                setState(() {
                  _markers.clear();
                  _polylines.clear();
                  _carPosition = null;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: const Text('Would you like to request a new ride?'),
                    action: SnackBarAction(label: 'Yes', onPressed: _resetMapAndShowRideRequest),
                    duration: const Duration(seconds: 10),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white),
              child: const Text('OK'),
            ),
          ],
        ),
      );
    }
  }

  void _resetMapAndShowRideRequest() {
    setState(() {
      _markers.clear();
      _polylines.clear();
      _carPosition = null;
    });
    if (_currentPosition != null && _mapController.isCompleted) {
      _mapController.future.then((controller) => controller.animateCamera(CameraUpdate.newLatLngZoom(_currentPosition!, 15)));
    }
  }

  void _handleRideError(Map<String, dynamic> data) {
    final String message = data['message'] ?? 'An unknown error occurred';
    if (mounted) {
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: const Row(children: [Icon(Icons.error, color: Colors.red), SizedBox(width: 10), Text('Error')]),
          content: Text(message),
          actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('OK'))],
        ),
      );
    }
  }

  void _handleRideStarted(dynamic data) {
    Map<String, dynamic> rideData = data is String ? {'rideId': data} : data is Map<String, dynamic> ? data : {};
    final String rideId = rideData['rideId'] ?? '';
    final String driverId = rideData['driverId'] ?? '';
    LatLng? pickupLocation = rideData['pickupLocation'] != null ? LatLng(rideData['pickupLocation']['lat'], rideData['pickupLocation']['lng']) : null;
    LatLng? destination = rideData['destination'] != null ? LatLng(rideData['destination']['lat'], rideData['destination']['lng']) : null;

    if (mounted) {
      if (pickupLocation != null && destination != null) {
        setState(() {
          _markers.removeWhere((m) => m.markerId.value == 'pickup' || m.markerId.value == 'destination');
          _markers.add(Marker(markerId: const MarkerId('pickup'), position: pickupLocation, icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen)));
          _markers.add(Marker(markerId: const MarkerId('destination'), position: destination, icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed)));
        });
        _drawRouteAndAnimate(pickupLocation, destination);
      } else {
        _rideService.getRideById(rideId).then((ride) {
          if (mounted) {
            final pickupLatLng = LatLng(ride.pickupLocation['lat'], ride.pickupLocation['lng']);
            final destinationLatLng = LatLng(ride.destination['lat'], ride.destination['lng']);
            setState(() {
              _markers.removeWhere((m) => m.markerId.value == 'pickup' || m.markerId.value == 'destination');
              _markers.add(Marker(markerId: const MarkerId('pickup'), position: pickupLatLng, icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen)));
              _markers.add(Marker(markerId: const MarkerId('destination'), position: destinationLatLng, icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed)));
            });
            _drawRouteAndAnimate(pickupLatLng, destinationLatLng);
          }
        }).catchError((error) => debugPrint('Error fetching ride details: $error'));
      }
    }
  }

  Future<void> _handleRideAccepted(Map<String, dynamic> data) async {
    final String rideId = data['rideId'] ?? '';
    final String driverId = data['driverId'] ?? '';
    try {
      _showLoadingDialog('Loading ride information...');
      final ride = await _rideService.getRideById(rideId);
      final driverPosition = data['driverLocation'] != null
          ? LatLng(data['driverLocation']['lat'], data['driverLocation']['lng'])
          : LatLng(ride.pickupLocation['lat'] - 0.005, ride.pickupLocation['lng'] - 0.005);

      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      _startRideInBackground(
        driverPosition: driverPosition,
        pickupLocation: LatLng(ride.pickupLocation['lat'], ride.pickupLocation['lng']),
        destination: LatLng(ride.destination['lat'], ride.destination['lng']),
        rideId: rideId,
        driverId: driverId,
      );

      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => Dialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
            child: Container(
              padding: const EdgeInsets.all(20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(color: Colors.green.withOpacity(0.2), shape: BoxShape.circle),
                    child: const Icon(Icons.check_circle, color: Colors.green, size: 50),
                  ),
                  const SizedBox(height: 20),
                  const Text('Ride Accepted!', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
                  const SizedBox(height: 15),
                  const Text('A driver has accepted your ride request. Your ride will start now.', textAlign: TextAlign.center, style: TextStyle(fontSize: 16)),
                  const SizedBox(height: 20),
                  Container(
                    padding: const EdgeInsets.all(15),
                    decoration: BoxDecoration(color: Colors.grey.withOpacity(0.1), borderRadius: BorderRadius.circular(12)),
                    child: Column(
                      children: [
                        Row(children: [const Icon(Icons.person, color: Colors.blue, size: 20), const SizedBox(width: 8), Expanded(child: Text(data['driverInfo']?['name'] ?? 'Driver', style: const TextStyle(fontWeight: FontWeight.bold)))]),
                        const SizedBox(height: 8),
                        Row(children: [const Icon(Icons.star, color: Colors.amber, size: 20), const SizedBox(width: 8), Text('${data['driverInfo']?['rating'] ?? '4.5'} ★')]),
                        const SizedBox(height: 8),
                        Row(children: [const Icon(Icons.directions_car, color: Colors.green, size: 20), const SizedBox(width: 8), Expanded(child: Text('${data['driverInfo']?['car'] ?? 'Car'} • ${data['driverInfo']?['plate'] ?? 'Plate'}'))]),
                        const SizedBox(height: 8),
                        Row(children: [const Icon(Icons.access_time, color: Colors.orange, size: 20), const SizedBox(width: 8), Text('ETA: ${data['driverInfo']?['eta'] ?? '5 min'}')]),
                      ],
                    ),
                  ),
                  const SizedBox(height: 25),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _startRide(
                          driverPosition: driverPosition,
                          pickupLocation: LatLng(ride.pickupLocation['lat'], ride.pickupLocation['lng']),
                          destination: LatLng(ride.destination['lat'], ride.destination['lng']),
                          rideId: rideId,
                          driverId: driverId,
                        );
                      },
                      style: ElevatedButton.styleFrom(backgroundColor: Colors.green, foregroundColor: Colors.white, padding: const EdgeInsets.symmetric(vertical: 12), shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10))),
                      child: const Text('Track Ride', style: TextStyle(fontSize: 16)),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }
    } catch (e) {
      debugPrint('Error processing ride acceptance: $e');
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error processing ride acceptance: $e'), backgroundColor: Colors.red));
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Ride Accepted!'),
            content: const Text('A driver has accepted your ride request. Your ride will start soon.'),
            actions: [TextButton(onPressed: () { Navigator.pop(context); _startSimpleRide(); }, child: const Text('OK'))],
          ),
        );
      }
    }
  }

  Future<void> _handleRideShared(Map<String, dynamic> data) async {
    final String rideId = data['rideId'] ?? '';
    final String mergedWithRideId = data['mergedWithRideId'] ?? '';
    final String driverId = data['driverId'] ?? '';
    final driverInfo = data['driverInfo'];
    final driverLocation = data['driverLocation'];

    try {
      _showLoadingDialog('Loading shared ride information...');
      await _rideService.getRideById(mergedWithRideId);
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
      }

      if (driverLocation != null) {
        final driverLatLng = LatLng(driverLocation['lat'], driverLocation['lng']);
        setState(() {
          _markers.removeWhere((m) => m.markerId.value == 'driver');
          _markers.add(
            Marker(
              markerId: const MarkerId('driver'),
              position: driverLatLng,
              icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
              infoWindow: const InfoWindow(title: 'Driver', snippet: 'On the way to you (shared ride)'),
            ),
          );
          _currentRideId = mergedWithRideId;
          _currentRideStatus = 'shared';
          _currentDriverId = driverId;
          _currentDriverInfo = driverInfo;
          _isSharedRide = true;
        });
        _listenForDriverLocationUpdates(driverId);
        _showSharedRideInfoDialog();
      }
    } catch (e) {
      debugPrint('Error processing ride sharing: $e');
      if (mounted) {
        Navigator.of(context, rootNavigator: true).pop();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error processing ride sharing: $e'), backgroundColor: Colors.red));
      }
    }
  }

  void _showSharedRideInfoDialog() {
    if (!mounted) return;
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Shared Ride'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Your ride will be shared with another rider.', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 12),
            const Text('• The route may adjust slightly to accommodate the other rider'),
            const Text('• The ride may take slightly longer'),
            const Text('• You’ll receive a discount on the fare'),
            const SizedBox(height: 16),
            const Text('You can track the ride on the map.', style: TextStyle(fontStyle: FontStyle.italic)),
          ],
        ),
        actions: [TextButton(onPressed: () => Navigator.pop(context), child: const Text('OK'))],
      ),
    );
  }
}

class AddressPopup extends StatelessWidget {
  final LatLng? currentPosition;
  final Function(String) onSearch;
  final Function(LatLng, LatLng) onRouteConfirmed;
  final Function(LatLng) onSendRideRequest;
  final String? initialToAddress;
  final LatLng? initialToPosition;

  const AddressPopup({
    super.key,
    this.currentPosition,
    required this.onSearch,
    required this.onRouteConfirmed,
    required this.onSendRideRequest,
    this.initialToAddress,
    this.initialToPosition,
  });

  @override
  Widget build(BuildContext context) {
    final TextEditingController toController = TextEditingController(text: initialToAddress ?? '');

    return Padding(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom, left: 16, right: 16, top: 16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: toController,
            decoration: const InputDecoration(labelText: 'Destination', border: OutlineInputBorder(), prefixIcon: Icon(Icons.location_on)),
            onSubmitted: (value) {
              if (value.isNotEmpty) onSearch(value);
            },
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () {
                  if (currentPosition != null && initialToPosition != null) {
                    onRouteConfirmed(currentPosition!, initialToPosition!);
                    Navigator.pop(context);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please select a destination')));
                  }
                },
                child: const Text('Preview Route'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (initialToPosition != null) {
                    onSendRideRequest(initialToPosition!);
                    Navigator.pop(context);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Please select a destination')));
                  }
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.green),
                child: const Text('Request Ride'),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }
}
