# C/C++ build system timings
generate_cxx_metadata
  [gap of 120ms]
  create-invalidation-state 170ms
  [gap of 54ms]
  write-metadata-json-to-file 47ms
generate_cxx_metadata completed in 392ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 25ms]
  create-invalidation-state 100ms
  [gap of 38ms]
  write-metadata-json-to-file 39ms
generate_cxx_metadata completed in 204ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 287ms]
  create-invalidation-state 576ms
  [gap of 428ms]
  write-metadata-json-to-file 289ms
generate_cxx_metadata completed in 1581ms

