const bcrypt = require('bcryptjs');

// In-memory user storage
const users = [];

class UserStorage {
  constructor() {
    console.log('UserStorage initialized with in-memory storage');
  }

  // Create a new user
  async create(userData) {
    try {
      // Check if user already exists
      const existingUser = this.findByEmail(userData.email);
      if (existingUser) {
        throw new Error('Email is already registered');
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(userData.password, 12);

      // Create user object
      const user = {
        _id: `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        firstName: userData.firstName,
        lastName: userData.lastName,
        email: userData.email.toLowerCase(),
        password: hashedPassword,
        phone: userData.phone,
        role: userData.role,
        vehicleModel: userData.vehicleModel || null,
        licensePlate: userData.licensePlate || null,
        profileImage: null,
        isEmailVerified: false,
        isActive: true,
        otp: null,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Add to storage
      users.push(user);

      console.log(`User created with ID: ${user._id}`);
      return user;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  // Find user by email
  findByEmail(email) {
    return users.find(user => user.email === email.toLowerCase()) || null;
  }

  // Find user by ID
  findById(userId) {
    return users.find(user => user._id === userId) || null;
  }

  // Update user
  async update(userId, updates) {
    try {
      const userIndex = users.findIndex(user => user._id === userId);
      if (userIndex === -1) {
        return null;
      }

      // Update user
      users[userIndex] = {
        ...users[userIndex],
        ...updates,
        updatedAt: new Date()
      };

      console.log(`User updated: ${userId}`);
      return users[userIndex];
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  // Compare password
  async comparePassword(user, candidatePassword) {
    return bcrypt.compare(candidatePassword, user.password);
  }

  // Generate OTP
  generateOTP(user) {
    const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit OTP
    user.otp = {
      code: otp,
      expiresAt: new Date(Date.now() + 10 * 60 * 1000) // 10 minutes
    };
    return otp;
  }

  // Verify OTP
  verifyOTP(user, candidateOTP) {
    if (!user.otp || !user.otp.code || !user.otp.expiresAt) {
      return false;
    }
    
    if (user.otp.expiresAt < new Date()) {
      return false; // OTP expired
    }
    
    return user.otp.code === candidateOTP;
  }

  // Get all users (for debugging)
  getAllUsers() {
    return users.map(user => ({
      _id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      isEmailVerified: user.isEmailVerified,
      createdAt: user.createdAt
    }));
  }

  // Clear all users (for testing)
  clearAll() {
    users.length = 0;
    console.log('All users cleared from storage');
  }
}

module.exports = new UserStorage();
