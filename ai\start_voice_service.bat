@echo off
echo Starting voice command processing service...

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Python is not installed. Please install Python and try again.
    exit /b 1
)

REM Install dependencies if not already installed
echo Installing dependencies...
pip install -r requirements.txt

REM Start the service
echo Starting voice command processing service...
python voice_command_processor.py
