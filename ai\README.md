# معالج الأوامر الصوتية للتطبيق الذكي لمشاركة الرحلات
# Voice Command Processor for Smart Ride-Sharing Application

هذه الخدمة توفر واجهة برمجة تطبيقات REST لمعالجة الأوامر الصوتية المتعلقة بوظائف مشاركة الرحلات. تستخدم تقنية التعرف على الكلام لتحويل الصوت إلى نص ومعالجة اللغة الطبيعية لتفسير الأوامر.

This service provides a REST API for processing voice commands related to ride-sharing functionality. It uses speech recognition to convert audio to text and natural language processing to interpret commands.

## الميزات / Features

- تحويل الكلام إلى نص باستخدام واجهة برمجة التطبيقات للتعرف على الكلام من Google
- معالجة اللغة الطبيعية لتحديد قصد الأمر
- دعم الأوامر باللغتين العربية والإنجليزية
- استخراج معلومات الموقع من نص الأمر
- إدارة سياق المحادثة
- واجهة برمجة تطبيقات REST للتكامل مع تطبيق Flutter

- Speech-to-text conversion using Google's speech recognition API
- Natural language processing to identify command intent
- Support for both Arabic and English commands
- Location extraction from command text
- Conversation context management
- REST API for integration with the Flutter app

## التثبيت / Installation

1. تأكد من تثبيت Python 3.7+ على جهازك
2. قم بتثبيت المكتبات المطلوبة:

```bash
pip install -r requirements.txt
```

3. إذا كنت تستخدم نظام Linux، قد تحتاج إلى تثبيت مكتبات إضافية لـ PyAudio:

```bash
sudo apt-get install portaudio19-dev python-pyaudio
```

## تشغيل الخدمة / Running the Service

قم بتشغيل الخدمة باستخدام الأمر التالي:

```bash
# على نظام Linux/Mac
./start_voice_service.sh

# على نظام Windows
start_voice_service.bat
```

أو يمكنك تشغيل الملف مباشرة:

```bash
python voice_command_processor.py
```

ستبدأ الخدمة على العنوان http://localhost:5000 بشكل افتراضي.

## واجهات برمجة التطبيقات / API Endpoints

### معالجة الأمر الصوتي / Process Voice Command

**النقطة النهائية:** `POST /api/voice/process`

**الطلب:**
- بيانات النموذج مع ملف صوتي: `audio=@recording.wav`
- أو JSON مع نص: `{"text": "طلب رحلة إلى المطار"}`

**الاستجابة:**
```json
{
  "success": true,
  "conversation_id": "550e8400-e29b-41d4-a716-446655440000",
  "command": {
    "type": "request_ride",
    "text": "طلب رحلة إلى المطار",
    "confidence": 0.9
  },
  "response": {
    "message": "جاري طلب رحلة إلى المطار. هل تريد تأكيد الطلب؟",
    "action": "request_ride",
    "location": {
      "name": "airport",
      "lat": 30.1219,
      "lng": 31.4056,
      "address": "Cairo International Airport"
    },
    "requires_confirmation": true
  }
}
```

### الاستماع للأمر الصوتي / Listen for Voice Command

**النقطة النهائية:** `POST /api/voice/listen`

**الطلب:**
```json
{
  "timeout": 5
}
```

**الاستجابة:**
```json
{
  "success": true,
  "text": "طلب رحلة إلى المطار",
  "language": "ar-SA"
}
```

### معالجة الأمر المباشر / Process Direct Command

**النقطة النهائية:** `POST /api/voice/command`

**الطلب:**
```json
{
  "command": "أين السائق"
}
```

**الاستجابة:**
```json
{
  "success": true,
  "conversation_id": "550e8400-e29b-41d4-a716-446655440000",
  "command": {
    "type": "driver_location",
    "text": "أين السائق",
    "confidence": 0.9
  },
  "response": {
    "message": "السائق في الطريق إليك، سيصل خلال 5 دقائق تقريباً.",
    "action": "show_driver_location",
    "driver_eta": 5,
    "driver": {
      "name": "أحمد",
      "car": "تويوتا كورولا",
      "plate": "ABC 123",
      "rating": 4.8
    }
  }
}
```

### التحقق من حالة الخدمة / Check Service Status

**النقطة النهائية:** `GET /api/voice/status`

**الاستجابة:**
```json
{
  "success": true,
  "status": "running",
  "version": "1.0.0",
  "active_conversations": 3
}
```

## الأوامر المدعومة / Supported Commands

يمكن للخدمة التعرف على الأوامر التالية ومعالجتها باللغتين العربية والإنجليزية:

1. **طلب رحلة**: "طلب رحلة"، "اطلب رحلة"، "book ride"، "request ride"
2. **إلغاء الرحلة**: "إلغاء الرحلة"، "ألغي الرحلة"، "cancel ride"، "stop ride"
3. **موقع السائق**: "أين السائق"، "وين السائق"، "where is driver"، "driver location"
4. **تغيير الوجهة**: "تغيير الوجهة"، "غير الوجهة"، "change destination"، "update destination"
5. **مشاركة الرحلة**: "مشاركة الرحلة"، "شارك الرحلة"، "share ride"، "share trip"
6. **تقييم الرحلة**: "تقييم الرحلة"، "قيم الرحلة"، "rate ride"، "rate trip"
7. **المساعدة**: "مساعدة"، "ساعدني"، "help"، "assist me"
8. **الصفحة الرئيسية**: "الصفحة الرئيسية"، "الرئيسية"، "home"، "main screen"
9. **العودة**: "العودة"، "رجوع"، "back"، "go back"

## التكامل مع Flutter / Integration with Flutter

تم إنشاء فئة خدمة في Flutter للتكامل مع معالج الأوامر الصوتية. يمكنك استخدام هذه الفئة للتفاعل مع الخدمة من تطبيق Flutter الخاص بك.

الملف: `project/lib/core/services/voice_command_service.dart`

كما تم إنشاء زر للأوامر الصوتية يمكن إضافته إلى أي شاشة في التطبيق.

الملف: `project/lib/features/voice_assistant/widgets/voice_command_button.dart`

مثال على استخدام الخدمة:

```dart
// في تطبيق Flutter الخاص بك
Future<void> processVoiceCommand() async {
  try {
    // استخدام وظيفة تسجيل الصوت الموجودة
    final audioFile = await _recordAudio();
    
    // إنشاء طلب متعدد الأجزاء
    var request = http.MultipartRequest(
      'POST',
      Uri.parse('http://localhost:5000/api/voice/process'),
    );
    
    // إضافة ملف الصوت
    request.files.add(
      await http.MultipartFile.fromPath(
        'audio',
        audioFile.path,
      ),
    );
    
    // إضافة معرف المستخدم ومعرف المحادثة إذا كانت متوفرة
    if (_userId != null) {
      request.headers['X-User-ID'] = _userId!;
    }
    if (_conversationId != null) {
      request.headers['X-Conversation-ID'] = _conversationId!;
    }
    
    // إرسال الطلب
    var response = await request.send();
    var responseData = await response.stream.bytesToString();
    var result = json.decode(responseData);
    
    // معالجة الاستجابة
    if (result['success']) {
      // حفظ معرف المحادثة للطلبات المستقبلية
      _conversationId = result['conversation_id'];
      
      // معالجة استجابة الأمر
      _handleCommandResponse(result);
    } else {
      _showError(result['error']);
    }
  } catch (e) {
    _showError('خطأ في معالجة الأمر الصوتي: $e');
  }
}
```

## التخصيص / Customization

يمكنك تخصيص الخدمة عن طريق:

1. إضافة المزيد من الكلمات الرئيسية للأوامر في قاموس `COMMAND_KEYWORDS`
2. إضافة المزيد من المواقع في قاموس `COMMON_LOCATIONS`
3. تحسين منطق معالجة اللغة الطبيعية في وظيفة `identify_command`
4. تعديل تنسيق الاستجابة في وظيفة `process_command`
