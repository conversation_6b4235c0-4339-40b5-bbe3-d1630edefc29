# C/C++ build system timings
generate_cxx_metadata
  execute-generate-process
    exec-configure 1476ms
    [gap of 16ms]
  execute-generate-process completed in 1498ms
  [gap of 24ms]
generate_cxx_metadata completed in 1527ms

# C/C++ build system timings
generate_cxx_metadata
  create-invalidation-state 13ms
  [gap of 21ms]
generate_cxx_metadata completed in 35ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 14ms]
  create-invalidation-state 37ms
  [gap of 17ms]
generate_cxx_metadata completed in 68ms

